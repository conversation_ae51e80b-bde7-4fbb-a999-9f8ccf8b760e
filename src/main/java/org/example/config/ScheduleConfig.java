package org.example.config;

import org.example.service.CreateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * 定时任务配置类
 * 每10分钟执行一次CreateService中的方法
 */
@Configuration
@EnableScheduling
public class ScheduleConfig {

    @Autowired
    private CreateService createService;

    /**
     * 定时任务：每10分钟执行一次
     * 调用CreateService中的createCarrier()和createCarrier1()方法
     */
    @Scheduled(initialDelay = 10 * 60 * 1000, fixedRate = 10 * 60 * 1000) // 10分钟后首次执行，然后每10分钟执行一次
    public void executeCreateCarrierTasks() {
        try {
            System.out.println("=== 定时任务开始执行 ===");
            System.out.println("执行时间: " + new java.util.Date());

            // 调用createCarrier()方法
            createService.createCarrier();

            // 调用createCarrier1()方法
            createService.createCarrier1();

            System.out.println("=== 定时任务执行完成 ===");
        } catch (Exception e) {
            System.err.println("定时任务执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }


}
