package org.example.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.example.model.*;
import org.example.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 创建服务类
 * 用于定时任务创建测试数据
 */
@Service
public class CreateService {

    private static final Logger logger = LoggerFactory.getLogger(CreateService.class);

    @Autowired
    private CarrierRepository carrierRepository;

    @Autowired
    private QuotationRecordRepository quotationRepository;

    @Autowired
    private RouteRepository routeRepository;

    @Autowired
    private RateRepository rateRepository;

    @Autowired
    private TransferRepository transferRepository;

    @Autowired
    private TransferDestinationRepository transferDestinationRepository;

    @Autowired
    private RemarkRepository remarkRepository;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 创建承运商数据
     * 从预定义数据创建基础承运商信息
     */
    @Transactional
    public void createCarrier() {
        logger.info("开始执行createCarrier()方法");

        try {
            // 创建基础承运商数据
            createBasicCarriers();

            // 导入test-data.json数据
            importTestData();

            logger.info("createCarrier()方法执行完成");
        } catch (Exception e) {
            logger.error("createCarrier()方法执行失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建承运商数据失败", e);
        }
    }

    /**
     * 创建承运商数据1
     * 从transfer-test-data.json创建中转相关数据
     */
    @Transactional
    public void createCarrier1() {
        logger.info("开始执行createCarrier1()方法");

        try {
            // 创建额外的承运商数据
            createAdditionalCarriers();

            // 导入transfer-test-data.json数据
            importTransferTestData();

            logger.info("createCarrier1()方法执行完成");
        } catch (Exception e) {
            logger.error("createCarrier1()方法执行失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建承运商数据1失败", e);
        }
    }

    /**
     * 创建基础承运商数据
     */
    private void createBasicCarriers() {
        logger.info("创建基础承运商数据");

        // 基础承运商数据
        String[][] carrierData = {
            {"中国国际航空", "CA", "北京市朝阳区首都机场", "010-95583", "张经理"},
            {"中国东方航空", "MU", "上海市浦东新区浦东机场", "021-95530", "李经理"},
            {"中国南方航空", "CZ", "广州市白云区白云机场", "020-95539", "王经理"},
            {"汉莎航空", "LH", "德国法兰克福机场", "+49-69-696-0", "Hans Mueller"},
            {"测试承运商", "TEST", "测试地址", "123-456-7890", "测试联系人"}
        };

        for (String[] data : carrierData) {
            createCarrierIfNotExists(data[0], data[1], data[2], data[3], data[4]);
        }
    }

    /**
     * 创建额外的承运商数据
     */
    private void createAdditionalCarriers() {
        logger.info("创建额外承运商数据");

        // 额外承运商数据
        String[][] additionalCarrierData = {
            {"法国航空", "AF", "法国巴黎戴高乐机场", "+33-1-41-56-78-00", "Pierre Dubois"},
            {"英国航空", "BA", "英国伦敦希思罗机场", "+44-20-8738-5050", "John Smith"},
            {"荷兰皇家航空", "KL", "荷兰阿姆斯特丹史基浦机场", "+31-20-649-9123", "Jan van der Berg"},
            {"中转测试承运商", "TRANSFER", "中转测试地址", "************", "中转联系人"}
        };

        for (String[] data : additionalCarrierData) {
            createCarrierIfNotExists(data[0], data[1], data[2], data[3], data[4]);
        }
    }

    /**
     * 创建承运商（如果不存在）
     */
    private void createCarrierIfNotExists(String name, String code, String address, String phone, String contacts) {
        Optional<Carrier> existingCarrier = carrierRepository.findByCode(code);
        if (existingCarrier.isPresent()) {
            logger.info("承运商已存在，跳过创建: {} ({})", name, code);
            return;
        }

        Carrier carrier = new Carrier();
        carrier.setName(name);
        carrier.setCode(code);
        carrier.setAddress(address);
        carrier.setPhone(phone);
        carrier.setContacts(contacts);
        carrier.setCreateTime(LocalDateTime.now());
        carrier.setUpdateTime(LocalDateTime.now());

        carrierRepository.save(carrier);
        logger.info("成功创建承运商: {} ({})", name, code);
    }

    /**
     * 导入test-data.json数据
     */
    private void importTestData() {
        logger.info("开始导入test-data.json数据");

        try {
            ClassPathResource resource = new ClassPathResource("test-data.json");
            InputStream inputStream = resource.getInputStream();

            Map<String, Object> quotationData = objectMapper.readValue(inputStream, new TypeReference<Map<String, Object>>() {});

            // 检查报价单是否已存在
            String quoteNumber = (String) quotationData.get("quote_number");
            Optional<QuotationRecord> existingRecord = quotationRepository.findByQuoteNumber(quoteNumber);
            if (existingRecord.isPresent()) {
                logger.info("报价单已存在，跳过导入: {}", quoteNumber);
                return;
            }

            // 导入报价单数据
            importQuotationData(quotationData);
            logger.info("成功导入test-data.json数据");

        } catch (IOException e) {
            logger.error("读取test-data.json文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("导入测试数据失败", e);
        }
    }

    /**
     * 导入transfer-test-data.json数据
     */
    private void importTransferTestData() {
        logger.info("开始导入transfer-test-data.json数据");

        try {
            ClassPathResource resource = new ClassPathResource("transfer-test-data.json");
            InputStream inputStream = resource.getInputStream();

            Map<String, Object> quotationData = objectMapper.readValue(inputStream, new TypeReference<Map<String, Object>>() {});

            // 检查报价单是否已存在
            String quoteNumber = (String) quotationData.get("quote_number");
            Optional<QuotationRecord> existingRecord = quotationRepository.findByQuoteNumber(quoteNumber);
            if (existingRecord.isPresent()) {
                logger.info("报价单已存在，跳过导入: {}", quoteNumber);
                return;
            }

            // 导入报价单数据
            importQuotationData(quotationData);
            logger.info("成功导入transfer-test-data.json数据");

        } catch (IOException e) {
            logger.error("读取transfer-test-data.json文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("导入中转测试数据失败", e);
        }
    }

    /**
     * 导入报价单数据
     * 复用QuotationController中的导入逻辑
     */
    private void importQuotationData(Map<String, Object> quotationData) {
        logger.info("开始导入报价单数据: {}", quotationData.get("quote_number"));

        // 1. 创建并保存主表记录
        QuotationRecord record = new QuotationRecord();
        record.setCurrency(String.valueOf(quotationData.get("currency")));
        record.setQuoteDate(LocalDate.parse(String.valueOf(quotationData.get("quote_date"))));
        record.setQuoteNumber(String.valueOf(quotationData.get("quote_number")));
        record.setSourceFile(""); // 定时任务创建的数据没有源文件
        record.setStatus(String.valueOf(quotationData.get("status")));
        record.setValidFrom(LocalDate.parse(String.valueOf(quotationData.get("valid_from"))));
        record.setValidTo(LocalDate.parse(String.valueOf(quotationData.get("valid_to"))));
        record.setVendorCode(String.valueOf(quotationData.get("vendor_code")));

        // 保存主表记录
        QuotationRecord savedRecord = quotationRepository.save(record);
        Long quotationId = savedRecord.getId();

        // 2. 处理并保存备注信息
        List<Map<String, Object>> remarksData = (List<Map<String, Object>>) quotationData.get("remarks");
        if (remarksData != null) {
            for (Map<String, Object> remarkData : remarksData) {
                Remark remark = new Remark();
                remark.setContent((String) remarkData.get("content"));
                remark.setImportant((Boolean) remarkData.get("is_important"));
                remark.setType((String) remarkData.get("type"));
                remark.setQuotationId(quotationId);
                remarkRepository.save(remark);
            }
        }

        // 3. 处理并保存路线信息
        List<Map<String, Object>> routesData = (List<Map<String, Object>>) quotationData.get("routes");
        if (routesData != null) {
            for (Map<String, Object> routeData : routesData) {
                Route route = new Route();
                route.setMRate(((Number) routeData.get("M-rate")).doubleValue());
                route.setNRate(((Number) routeData.get("N-rate")).doubleValue());
                route.setCarrierCode((String) routeData.get("carrier_code"));
                route.setDestination((String) routeData.get("destination"));
                route.setFrequency((String) routeData.get("frequency"));
                route.setOrigin((String) routeData.get("origin"));
                route.setQuotationId(quotationId);

                // 保存路线
                Route savedRoute = routeRepository.save(route);
                Long routeId = savedRoute.getId();

                // 4. 处理并保存费率信息
                List<Map<String, Object>> ratesData = (List<Map<String, Object>>) routeData.get("rates");
                if (ratesData != null) {
                    for (Map<String, Object> rateData : ratesData) {
                        Rate rate = new Rate();
                        rate.setDensityFactor(((Number) rateData.get("density_factor")).doubleValue());
                        rate.setDensityType((String) rateData.get("density_type"));
                        rate.setMaxWt(((Number) rateData.get("max_wt")).doubleValue());
                        rate.setMinWt(((Number) rateData.get("min_wt")).doubleValue());
                        rate.setPrice(((Number) rateData.get("price")).doubleValue());
                        rate.setRouteId(routeId);
                        rateRepository.save(rate);
                    }
                }

                // 5. 处理并保存中转信息
                List<Map<String, Object>> transfersData = (List<Map<String, Object>>) routeData.get("transfers");
                if (transfersData != null) {
                    for (Map<String, Object> transferData : transfersData) {
                        Transfer transfer = new Transfer();
                        transfer.setCostCurrency((String) transferData.get("cost_currency"));
                        transfer.setFromPort((String) transferData.get("from_port"));
                        transfer.setTransferCost(((Number) transferData.get("transfer_cost")).doubleValue());
                        transfer.setRouteId(routeId);

                        // 保存中转
                        Transfer savedTransfer = transferRepository.save(transfer);
                        Long transferId = savedTransfer.getId();

                        // 6. 处理并保存中转目的地
                        List<Map<String, Object>> destinationsData = (List<Map<String, Object>>) transferData.get("destinations");
                        if (destinationsData != null) {
                            for (Map<String, Object> destData : destinationsData) {
                                TransferDestination destination = new TransferDestination();
                                destination.setToPort((String) destData.get("to_port"));
                                destination.setTransferId(transferId);
                                transferDestinationRepository.save(destination);
                            }
                        }
                    }
                }
            }
        }

        logger.info("成功导入报价单数据: {}", quotationData.get("quote_number"));
    }
}
