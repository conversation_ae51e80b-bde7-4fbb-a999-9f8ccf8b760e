package org.example.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.example.model.*;
import org.example.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 创建服务类
 * 用于定时任务创建测试数据
 */
@Service
public class CreateService {

    private static final Logger logger = LoggerFactory.getLogger(CreateService.class);

    @Autowired
    private CarrierRepository carrierRepository;

    @Autowired
    private QuotationRecordRepository quotationRepository;

    @Autowired
    private RouteRepository routeRepository;

    @Autowired
    private RateRepository rateRepository;

    @Autowired
    private TransferRepository transferRepository;

    @Autowired
    private TransferDestinationRepository transferDestinationRepository;

    @Autowired
    private RemarkRepository remarkRepository;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 创建承运商数据
     * 从预定义数据创建基础承运商信息
     */
    @Transactional
    public void createCarrier() {
        logger.info("开始执行createCarrier()方法");

        try {
            // 创建基础承运商数据
            createBasicCarriers();

            // 导入test-data.json数据
            importTestData();

            logger.info("createCarrier()方法执行完成");
        } catch (Exception e) {
            logger.error("createCarrier()方法执行失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建承运商数据失败", e);
        }
    }

    /**
     * 创建承运商数据1
     * 从transfer-test-data.json创建中转相关数据
     */
    @Transactional
    public void createCarrier1() {
        logger.info("开始执行createCarrier1()方法");

        try {
            // 创建额外的承运商数据
            createAdditionalCarriers();

            // 导入transfer-test-data.json数据
            importTransferTestData();

            logger.info("createCarrier1()方法执行完成");
        } catch (Exception e) {
            logger.error("createCarrier1()方法执行失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建承运商数据1失败", e);
        }
    }

    /**
     * 创建基础承运商数据
     */
    private void createBasicCarriers() {
        logger.info("创建基础承运商数据");

        // 基础承运商数据
        String[][] carrierData = {
            {"中国国际航空", "CA", "北京市朝阳区首都机场", "010-95583", "张经理"},
            {"中国东方航空", "MU", "上海市浦东新区浦东机场", "021-95530", "李经理"},
            {"中国南方航空", "CZ", "广州市白云区白云机场", "020-95539", "王经理"},
            {"汉莎航空", "LH", "德国法兰克福机场", "+49-69-696-0", "Hans Mueller"},
            {"测试承运商", "TEST", "测试地址", "123-456-7890", "测试联系人"}
        };

        for (String[] data : carrierData) {
            createCarrierIfNotExists(data[0], data[1], data[2], data[3], data[4]);
        }
    }

    /**
     * 创建额外的承运商数据
     */
    private void createAdditionalCarriers() {
        logger.info("创建额外承运商数据");

        // 额外承运商数据
        String[][] additionalCarrierData = {
            {"法国航空", "AF", "法国巴黎戴高乐机场", "+33-1-41-56-78-00", "Pierre Dubois"},
            {"英国航空", "BA", "英国伦敦希思罗机场", "+44-20-8738-5050", "John Smith"},
            {"荷兰皇家航空", "KL", "荷兰阿姆斯特丹史基浦机场", "+31-20-649-9123", "Jan van der Berg"},
            {"中转测试承运商", "TRANSFER", "中转测试地址", "999-888-7777", "中转联系人"}
        };

        for (String[] data : additionalCarrierData) {
            createCarrierIfNotExists(data[0], data[1], data[2], data[3], data[4]);
        }
    }
