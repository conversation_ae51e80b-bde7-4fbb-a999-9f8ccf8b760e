package org.example.service;

import org.example.entity.User;
import org.example.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class UserService {

    @Autowired
    private UserRepository userRepository;

    /**
     * 用户注册
     */
    public User register(String username, String password, String phone, String name) {
        // 检查用户名是否已存在
        if (userRepository.existsByUsername(username)) {
            throw new RuntimeException("用户名已存在");
        }

        // 创建新用户
        User user = new User();
        user.setUsername(username);
        user.setPassword(password); // 实际项目中应该加密密码
        user.setPhone(phone);
        user.setName(name);
        user.setRealname("query"); // 默认为普通用户
        user.setCreateTime(LocalDateTime.now());

        return userRepository.save(user);
    }

    /**
     * 用户登录
     */
    public User login(String username, String password) {
        Optional<User> userOpt = findByUsernameOptional(username);
        if (!userOpt.isPresent()) {
            throw new RuntimeException("用户不存在");
        }

        User user = userOpt.get();
        if (!password.equals(user.getPassword())) { // 实际项目中应该验证加密密码
            throw new RuntimeException("密码错误");
        }

        return user;
    }

    /**
     * 根据用户名查找用户（返回Optional）
     */
    public Optional<User> findByUsernameOptional(String username) {
        return userRepository.findByUsername(username);
    }

    /**
     * 根据用户名查找用户（返回User对象，用于兼容性）
     */
    public User findByUsername(String username) {
        Optional<User> userOpt = userRepository.findByUsername(username);
        return userOpt.orElse(null);
    }

    /**
     * 检查用户名是否存在
     */
    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }

    /**
     * 获取所有用户列表
     */
    public List<User> getAllUsers() {
        return userRepository.findAll();
    }

    /**
     * 根据ID查找用户
     */
    public Optional<User> findById(Long id) {
        return userRepository.findById(id);
    }

    /**
     * 更新用户信息
     */
    public User updateUser(User user) {
        return userRepository.save(user);
    }

    /**
     * 删除用户
     */
    public void deleteUser(Long id) {
        userRepository.deleteById(id);
    }

    /**
     * 创建新用户
     */
    public User createUser(User user) {
        // 设置创建时间
        user.setCreateTime(LocalDateTime.now());
        return userRepository.save(user);
    }
}
