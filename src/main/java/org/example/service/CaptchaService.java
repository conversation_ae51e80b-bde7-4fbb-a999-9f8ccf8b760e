package org.example.service;

import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class CaptchaService {

    // 存储验证码的Map，实际项目中应该使用Redis
    private final Map<String, String> captchaStore = new ConcurrentHashMap<>();
    
    private final Random random = new Random();
    
    // 验证码字符集
    private final String CHARS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    
    /**
     * 生成验证码
     */
    public Map<String, String> generateCaptcha() {
        // 生成4位随机验证码
        String code = generateRandomCode(4);
        
        // 生成唯一标识
        String captchaId = "captcha_" + System.currentTimeMillis() + "_" + random.nextInt(1000);
        
        // 存储验证码（5分钟有效期）
        captchaStore.put(captchaId, code);
        
        // 生成验证码图片
        String imageBase64 = generateCaptchaImage(code);
        
        Map<String, String> result = new HashMap<>();
        result.put("captchaId", captchaId);
        result.put("image", "data:image/png;base64," + imageBase64);
        
        return result;
    }
    
    /**
     * 验证验证码
     */
    public boolean verifyCaptcha(String captchaId, String userInput) {
        if (captchaId == null || userInput == null) {
            return false;
        }
        
        String storedCode = captchaStore.get(captchaId);
        if (storedCode == null) {
            return false;
        }
        
        // 验证后删除验证码
        captchaStore.remove(captchaId);
        
        return storedCode.equalsIgnoreCase(userInput);
    }
    
    /**
     * 生成随机验证码字符串
     */
    private String generateRandomCode(int length) {
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < length; i++) {
            code.append(CHARS.charAt(random.nextInt(CHARS.length())));
        }
        return code.toString();
    }
    
    /**
     * 生成验证码图片
     */
    private String generateCaptchaImage(String code) {
        int width = 120;
        int height = 40;
        
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();
        
        // 设置背景色
        g.setColor(Color.WHITE);
        g.fillRect(0, 0, width, height);
        
        // 设置字体
        g.setFont(new Font("Arial", Font.BOLD, 20));
        
        // 绘制验证码字符
        for (int i = 0; i < code.length(); i++) {
            // 随机颜色
            g.setColor(new Color(random.nextInt(255), random.nextInt(255), random.nextInt(255)));
            
            // 随机位置
            int x = 20 + i * 20 + random.nextInt(10);
            int y = 25 + random.nextInt(10);
            
            // 随机旋转
            g.rotate(Math.toRadians(random.nextInt(30) - 15), x, y);
            g.drawString(String.valueOf(code.charAt(i)), x, y);
            g.rotate(-Math.toRadians(random.nextInt(30) - 15), x, y);
        }
        
        // 添加干扰线
        for (int i = 0; i < 5; i++) {
            g.setColor(new Color(random.nextInt(255), random.nextInt(255), random.nextInt(255)));
            g.drawLine(random.nextInt(width), random.nextInt(height), 
                      random.nextInt(width), random.nextInt(height));
        }
        
        g.dispose();
        
        // 转换为Base64
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(image, "png", baos);
            return Base64.getEncoder().encodeToString(baos.toByteArray());
        } catch (IOException e) {
            throw new RuntimeException("生成验证码图片失败", e);
        }
    }
}
