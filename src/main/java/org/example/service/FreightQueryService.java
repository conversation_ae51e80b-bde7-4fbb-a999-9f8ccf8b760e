package org.example.service;

import org.example.model.FreightCalculation;
import org.example.model.Rate;
import org.example.model.Route;
import org.example.model.QuotationRecord;
import org.example.repository.RateRepository;
import org.example.repository.RouteRepository;
import org.example.repository.QuotationRecordRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class FreightQueryService {
    @Autowired
    private RateRepository rateRepository;
    @Autowired
    private QuotationRecordRepository quotationRecordRepository;
    @Autowired
    private RouteRepository routeRepository;

    // 保持向后兼容的方法
    public List<FreightCalculation> calculateFreight(double volume, double weight) {
        return calculateFreight(volume, weight, null, null);
    }

    // 新的计算方法，支持始发地和目的地筛选
    public List<FreightCalculation> calculateFreight(double volume, double weight, String origin, String destination) {
        // 第一步：计算密度
        if (volume <= 0) {
            return new ArrayList<>();
        }
        double density = weight / volume;

        // 第二步：获取所有报价单
        List<QuotationRecord> quotations = quotationRecordRepository.findAll();
        List<FreightCalculation> result = new ArrayList<>();

        // 第三步：遍历报价单，根据路线信息查询对应费率
        for (QuotationRecord quotation : quotations) {
            // 获取报价单对应的所有路线
            List<Route> routes = routeRepository.findByQuotationId(quotation.getId());

            for (Route route : routes) {
                // 根据始发地和目的地筛选路线
                if (!isRouteMatched(route, origin, destination)) {
                    continue;
                }

                // 获取该路线对应的所有费率
                List<Rate> ratesForRoute = rateRepository.findByRouteId(route.getId());
                if (ratesForRoute.isEmpty()) {
                    continue;
                }

                // 第四步：根据实际密度找到对应的密度等级
                double matchedDensityFactor = findMatchedDensityFactor(ratesForRoute, density);
                if (matchedDensityFactor <= 0) {
                    continue;
                }

                // 第五步：在匹配的密度等级下，查找对应的重量区间价格
                Rate matchedRate = findRateByDensityAndWeight(ratesForRoute, matchedDensityFactor, weight);
                if (matchedRate != null) {
                    FreightCalculation calc = new FreightCalculation();
                    calc.setQuotationId(quotation.getId());
                    calc.setQuoteNumber(quotation.getQuoteNumber());
                    calc.setVolume(volume);
                    calc.setWeight(weight);
                    calc.setDensity(density);
                    calc.setRateId(matchedRate.getId());
                    calc.setOrigin(route.getOrigin());
                    calc.setDestination(route.getDestination());
                    calc.setCarrierCode(route.getCarrierCode());

                    // 设置航司信息（如果有的话，暂时使用承运商代码）
                    calc.setAirline(route.getCarrierCode());

                    // 使用新的综合计算方法
                    calc.calculateFreightDetails(matchedRate.getPrice(), matchedDensityFactor, route.getMRate(), route.getNRate());

                    // 生成报价单详情链接
                    calc.setQuoteDetailUrl("/detail.html?id=" + quotation.getId());

                    result.add(calc);
                }
            }
        }
        return result;
    }

    /**
     * 查找匹配的密度等级
     * 例如：实际密度为120，密度等级有80、100、300、400、500，应该返回300
     */
    private double findMatchedDensityFactor(List<Rate> rates, double actualDensity) {
        // 获取所有不同的密度等级并排序
        List<Double> densityFactors = rates.stream()
                .map(Rate::getDensityFactor)
                .distinct()
                .sorted()
                .collect(Collectors.toList());

        // 找到第一个大于等于实际密度的密度等级
        for (Double factor : densityFactors) {
            if (factor >= actualDensity) {
                return factor;
            }
        }
        return 0; // 如果没有找到合适的密度等级，返回0
    }

    /**
     * 在指定密度等级下查找匹配的重量区间
     */
    private Rate findRateByDensityAndWeight(List<Rate> rates, double densityFactor, double weight) {
        // 筛选出指定密度等级的所有费率记录
        List<Rate> matchingRates = rates.stream()
                .filter(r -> r.getDensityFactor() == densityFactor)
                .sorted(Comparator.comparing(Rate::getMinWt))
                .collect(Collectors.toList());

        // 在指定密度等级下找到匹配的重量区间
        for (Rate rate : matchingRates) {
            if (weight >= rate.getMinWt() && weight < rate.getMaxWt()) {
                return rate;
            }
        }
        return null; // 如果没有匹配的重量区间，返回null
    }

    /**
     * 检查路线是否匹配指定的始发地和目的地
     */
    private boolean isRouteMatched(Route route, String origin, String destination) {
        // 如果没有指定始发地和目的地，则匹配所有路线
        if ((origin == null || origin.trim().isEmpty()) &&
            (destination == null || destination.trim().isEmpty())) {
            return true;
        }

        // 检查始发地匹配
        boolean originMatched = (origin == null || origin.trim().isEmpty()) ||
                               (route.getOrigin() != null && route.getOrigin().equalsIgnoreCase(origin.trim()));

        // 检查目的地匹配
        boolean destinationMatched = (destination == null || destination.trim().isEmpty()) ||
                                   (route.getDestination() != null && route.getDestination().equalsIgnoreCase(destination.trim()));

        return originMatched && destinationMatched;
    }

    /**
     * 获取所有可用的港口信息
     */
    public Map<String, List<String>> getAvailablePorts() {
        List<Route> allRoutes = routeRepository.findAll();

        Set<String> origins = new HashSet<>();
        Set<String> destinations = new HashSet<>();

        for (Route route : allRoutes) {
            if (route.getOrigin() != null && !route.getOrigin().trim().isEmpty()) {
                origins.add(route.getOrigin().trim().toUpperCase());
            }
            if (route.getDestination() != null && !route.getDestination().trim().isEmpty()) {
                destinations.add(route.getDestination().trim().toUpperCase());
            }
        }

        Map<String, List<String>> result = new HashMap<>();
        result.put("origins", new ArrayList<>(origins).stream().sorted().collect(Collectors.toList()));
        result.put("destinations", new ArrayList<>(destinations).stream().sorted().collect(Collectors.toList()));

        return result;
    }
}
