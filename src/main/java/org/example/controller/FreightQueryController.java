package org.example.controller;

import org.example.model.FreightCalculation;
import org.example.service.FreightQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/freight-query")
public class FreightQueryController {
    @Autowired
    private FreightQueryService freightQueryService;

    @GetMapping("/calculate")
    public List<FreightCalculation> calculate(@RequestParam double volume,
                                              @RequestParam double weight,
                                              @RequestParam(required = false) String origin,
                                              @RequestParam(required = false) String destination) {
        return freightQueryService.calculateFreight(volume, weight, origin, destination);
    }

    @GetMapping("/ports")
    public Map<String, List<String>> getAvailablePorts() {
        return freightQueryService.getAvailablePorts();
    }
} 