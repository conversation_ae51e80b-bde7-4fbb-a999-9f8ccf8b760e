package org.example.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.util.JSONPObject;
import lombok.RequiredArgsConstructor;
import org.example.model.*;
import org.example.service.BaiLianApiService;
import org.example.service.PdfParseService;
import org.example.repository.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

@RestController
@RequestMapping("/api/quotations")
@RequiredArgsConstructor
public class QuotationController {

    private final PdfParseService pdfParseService;
    private final BaiLianApiService baiLianApiService;
    private final QuotationRecordRepository quotationRepository;
    private final CarrierRepository carrierRepository;
    private final RouteRepository routeRepository;
    private final RemarkRepository remarkRepository;
    private final RateRepository rateRepository;
    private final TransferRepository transferRepository;
    private final TransferDestinationRepository transferDestinationRepository;

    @ResponseBody
    @RequestMapping(value = "/extract-text",method = RequestMethod.POST)
    public ResponseEntity<?> extractText(@RequestParam("file") MultipartFile file) {
        try {
            if (file == null || file.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "请选择要上传的文件");
                return ResponseEntity.badRequest().body(response);
            }

            String pdfContent = pdfParseService.parsePdfContent(file);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "PDF文本提取成功");
            response.put("content", pdfContent);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "PDF文本提取失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @PostMapping("/analyze")
    public ResponseEntity<?> analyzeContent(@RequestBody Map<String, String> request) {
        try {
            String content = request.get("content");
            if (content == null || content.trim().isEmpty()) {
                return createErrorResponse("内容不能为空");
            }

            // 调用百炼API进行分析，返回JSON字符串
            String analysisResult = baiLianApiService.parseLogisticsRules(content);

            // 将字符串转为对象
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> analysisMap;
            try {
                analysisMap = objectMapper.readValue(analysisResult, Map.class);
            } catch (Exception e) {
                return createErrorResponse("AI返回内容不是有效的JSON: " + e.getMessage());
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "解析成功");
            response.put("data", analysisMap);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return createErrorResponse("LLM解析失败: " + e.getMessage());
        }
    }

    @GetMapping("/carriers")
    public ResponseEntity<List<Carrier>> getAllCarriers() {
        return ResponseEntity.ok(carrierRepository.findAll());
    }

    @PostMapping("/carriers")
    public ResponseEntity<?> addCarrier(@RequestBody Carrier carrier) {
        try {
            // 检查名称是否已存在
            Optional<Carrier> existingByName = carrierRepository.findByName(carrier.getName());
            if (existingByName.isPresent()) {
                return createErrorResponse("承运商名称已存在");
            }

            // 检查代码是否已存在
            Optional<Carrier> existingByCode = carrierRepository.findByCode(carrier.getCode());
            if (existingByCode.isPresent()) {
                return createErrorResponse("承运商代码已存在");
            }

            Carrier savedCarrier = carrierRepository.save(carrier);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "承运商添加成功");
            response.put("data", savedCarrier);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return createErrorResponse("添加承运商失败: " + e.getMessage());
        }
    }

    @PutMapping("/carriers/{id}")
    public ResponseEntity<?> updateCarrier(@PathVariable Long id, @RequestBody Carrier carrier) {
        try {
            Optional<Carrier> existingCarrier = carrierRepository.findById(id);
            if (!existingCarrier.isPresent()) {
                return createErrorResponse("承运商不存在");
            }

            Carrier existing = existingCarrier.get();

            // 检查名称是否与其他承运商重复
            Optional<Carrier> existingByName = carrierRepository.findByName(carrier.getName());
            if (existingByName.isPresent() && !existingByName.get().getId().equals(id)) {
                return createErrorResponse("承运商名称已存在");
            }

            // 检查代码是否与其他承运商重复
            Optional<Carrier> existingByCode = carrierRepository.findByCode(carrier.getCode());
            if (existingByCode.isPresent() && !existingByCode.get().getId().equals(id)) {
                return createErrorResponse("承运商代码已存在");
            }

            // 更新字段
            existing.setName(carrier.getName());
            existing.setCode(carrier.getCode());
            existing.setAddress(carrier.getAddress());
            existing.setPhone(carrier.getPhone());
            existing.setContacts(carrier.getContacts());

            Carrier savedCarrier = carrierRepository.save(existing);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "承运商更新成功");
            response.put("data", savedCarrier);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return createErrorResponse("更新承运商失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/carriers/{id}")
    public ResponseEntity<?> deleteCarrier(@PathVariable Long id) {
        try {
            Optional<Carrier> existingCarrier = carrierRepository.findById(id);
            if (!existingCarrier.isPresent()) {
                return createErrorResponse("承运商不存在");
            }

            carrierRepository.deleteById(id);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "承运商删除成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return createErrorResponse("删除承运商失败: " + e.getMessage());
        }
    }

    @GetMapping("/carriers/{id}")
    public ResponseEntity<?> getCarrier(@PathVariable Long id) {
        try {
            Optional<Carrier> carrier = carrierRepository.findById(id);
            if (carrier.isPresent()) {
                return ResponseEntity.ok(carrier.get());
            } else {
                return createErrorResponse("承运商不存在");
            }
        } catch (Exception e) {
            return createErrorResponse("查询承运商失败: " + e.getMessage());
        }
    }

    @PostMapping("/import")
    public ResponseEntity<?> importQuotation(@RequestBody Map<String, Object> request) {
        try {
            String sourceFile = (String) request.get("source_file");
            String supplier = (String) request.get("supplier");
            String carrierCode = (String) request.get("carrier_code");

            // 2. 取出 content
            Map<String, Object> quotationData;
            if (request.containsKey("data")) {
                Object content = request.get("data");
                if (content instanceof Map) {
                    quotationData = (Map<String, Object>) content;
                } else {
                    return createErrorResponse("data 格式错误");
                }
            } else {
                quotationData = request;
            }

            // 参数验证
            String[] requiredFields = {"currency", "quote_date", "status",
                                     "valid_from", "valid_to", "vendor_code"};
            for (String field : requiredFields) {
                if (quotationData.get(field) == null || String.valueOf(quotationData.get(field)).trim().isEmpty()) {
                    return createErrorResponse("必填字段 " + field + " 不能为空");
                }
            }

            // 1. 先创建并保存主表记录
            QuotationRecord record = new QuotationRecord();
            record.setCurrency(String.valueOf(quotationData.get("currency")));

            try {
                record.setQuoteDate(LocalDate.parse(String.valueOf(quotationData.get("quote_date"))));
            } catch (Exception e) {
                return createErrorResponse("报价日期格式错误，应为 yyyy-MM-dd 格式");
            }

            // 获取报价单号，如果为空则使用sourceFile中的PDF文件名
            String quoteNumber = String.valueOf(quotationData.get("quote_number"));
            if (quoteNumber == null || quoteNumber.trim().isEmpty()) {
                if (sourceFile != null && !sourceFile.isEmpty()) {
                    // 从sourceFile路径中提取文件名
                    String fileName = sourceFile.substring(sourceFile.lastIndexOf('/') + 1);
                    // 移除文件扩展名
                    if (fileName.toLowerCase().endsWith(".pdf")) {
                        fileName = fileName.substring(0, fileName.length() - 4);
                    }
                    // 移除UUID前缀（如果存在）
                    if (fileName.contains("_")) {
                        fileName = fileName.substring(fileName.indexOf('_') + 1);
                    }
                    quoteNumber = fileName;
                } else {
                    return createErrorResponse("报价单号不能为空，且未提供源文件");
                }
            }
            record.setQuoteNumber(quoteNumber);

            // 检查报价单号是否已存在
            Optional<QuotationRecord> existingRecord = quotationRepository.findByQuoteNumber(record.getQuoteNumber());
            if (existingRecord.isPresent()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("message", "报价单号已存在");
                response.put("data", existingRecord.get());
                return ResponseEntity.ok(response);
            }

            record.setStatus(String.valueOf(quotationData.get("status")));

            try {
                record.setValidFrom(LocalDate.parse(String.valueOf(quotationData.get("valid_from"))));
                record.setValidTo(LocalDate.parse(String.valueOf(quotationData.get("valid_to"))));
            } catch (Exception e) {
                return createErrorResponse("有效期格式错误，应为 yyyy-MM-dd 格式");
            }

            record.setVendorCode(String.valueOf(quotationData.get("vendor_code")));

            if (carrierCode != null && !carrierCode.trim().isEmpty()) {
                record.setCarrierCode(carrierCode.trim());
            } else if (quotationData.get("carrier_code") != null) {
                record.setCarrierCode(String.valueOf(quotationData.get("carrier_code")));
            }

            if (supplier != null && !supplier.trim().isEmpty()) {
                record.setSupplier(supplier.trim());
            }

            if (sourceFile != null && !sourceFile.isEmpty()) {
                record.setSourceFile(sourceFile);
            } else if (quotationData.get("source_file") != null) {
                record.setSourceFile(String.valueOf(quotationData.get("source_file")));
            }

            // 保存主表记录
            QuotationRecord savedRecord = quotationRepository.save(record);
            Long quotationId = savedRecord.getId();

            // 2. 处理并保存备注信息
            List<Map<String, Object>> remarksData = (List<Map<String, Object>>) quotationData.get("remarks");
            if (remarksData != null) {
                for (Map<String, Object> remarkData : remarksData) {
                    Remark remark = new Remark();
                    remark.setContent((String) remarkData.get("content"));
                    remark.setImportant((Boolean) remarkData.get("is_important"));
                    remark.setType((String) remarkData.get("type"));
                    remark.setQuotationId(quotationId);  // 设置主表ID
                    remarkRepository.save(remark);  // 单独保存每条备注
                }
            }

            // 3. 处理并保存路线信息
            List<Map<String, Object>> routesData = (List<Map<String, Object>>) quotationData.get("routes");
            if (routesData != null) {
                for (Map<String, Object> routeData : routesData) {
                    Route route = new Route();
                    route.setMRate(((Number) routeData.get("M-rate")).doubleValue());
                    route.setNRate(((Number) routeData.get("N-rate")).doubleValue());
                    route.setCarrierCode((String) routeData.get("carrier_code"));
                    route.setDestination((String) routeData.get("destination"));
                    route.setFrequency((String) routeData.get("frequency"));
                    route.setOrigin((String) routeData.get("origin"));
                    route.setQuotationId(quotationId);  // 设置主表ID
                    
                    // 保存路线
                    Route savedRoute = routeRepository.save(route);
                    Long routeId = savedRoute.getId();

                    // 4. 处理并保存费率信息
                    List<Map<String, Object>> ratesData = (List<Map<String, Object>>) routeData.get("rates");
                    if (ratesData != null) {
                        for (Map<String, Object> rateData : ratesData) {
                            Rate rate = new Rate();
                            rate.setDensityFactor(((Number) rateData.get("density_factor")).doubleValue());
                            rate.setDensityType((String) rateData.get("density_type"));
                            rate.setMaxWt(((Number) rateData.get("max_wt")).doubleValue());
                            rate.setMinWt(((Number) rateData.get("min_wt")).doubleValue());
                            rate.setPrice(((Number) rateData.get("price")).doubleValue());
                            rate.setRouteId(routeId);// 设置路线ID
                            rateRepository.save(rate);  // 单独保存每条费率
                        }
                    }

                    // 5. 处理并保存中转信息
                    List<Map<String, Object>> transfersData = (List<Map<String, Object>>) routeData.get("transfers");
                    if (transfersData != null) {
                        for (Map<String, Object> transferData : transfersData) {
                            Transfer transfer = new Transfer();
                            transfer.setCostCurrency((String) transferData.get("cost_currency"));
                            transfer.setFromPort((String) transferData.get("from_port"));
                            transfer.setTransferCost(((Number) transferData.get("transfer_cost")).doubleValue());
                            transfer.setRouteId(routeId);  // 设置路线ID
                            
                            // 保存中转
                            Transfer savedTransfer = transferRepository.save(transfer);
                            Long transferId = savedTransfer.getId();

                            // 6. 处理并保存中转目的地
                            List<Map<String, Object>> destinationsData = (List<Map<String, Object>>) transferData.get("destinations");
                            if (destinationsData != null) {
                                for (Map<String, Object> destData : destinationsData) {
                                    TransferDestination destination = new TransferDestination();
                                    destination.setToPort((String) destData.get("to_port"));
                                    destination.setTransferId(transferId);  // 设置中转ID
                                    transferDestinationRepository.save(destination);  // 单独保存每个目的地
                                }
                            }
                        }
                    }
                }
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "导入成功");
            response.put("data", savedRecord);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            e.printStackTrace();
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "导入报价失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/list")
    public ResponseEntity<List<QuotationRecord>> getQuotationList(
            @RequestParam(value = "carrier", required = false) String carrier,
            @RequestParam(value = "status", required = false) String status) {
        List<QuotationRecord> all = quotationRepository.findAll();
        List<QuotationRecord> filtered = new ArrayList<>();

        for (QuotationRecord q : all) {
            boolean match = true;

            // 使用承运商代码字段进行过滤
            if (carrier != null && !carrier.isEmpty()) {
                if (!carrier.equalsIgnoreCase(q.getCarrierCode())) {
                    match = false;
                }
            }

            // 使用状态进行过滤
            if (status != null && !status.isEmpty()) {
                if (!status.equalsIgnoreCase(q.getStatus())) {
                    match = false;
                }
            }

            if (match) {
                filtered.add(q);
            }
        }
        return ResponseEntity.ok(filtered);
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getQuotationDetail(@PathVariable Long id) {
        Optional<QuotationRecord> record = quotationRepository.findById(id);
        if (record.isPresent()) {
            return ResponseEntity.ok(record.get());
        }
        return ResponseEntity.notFound().build();
    }

    private ResponseEntity<?> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        return ResponseEntity.badRequest().body(response);
    }

    @PostMapping("/upload")
    public ResponseEntity<?> upload(@RequestParam("file") MultipartFile file) {
        try {
            if (file == null || file.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "请选择要上传的文件");
                return ResponseEntity.badRequest().body(response);
            }

            // 保存文件并获取URL
            String fileUrl = pdfParseService.savePdfFile(file);
            // 解析PDF内容
            String pdfContent = pdfParseService.parsePdfContent(file);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "文件解析成功");
            response.put("content", pdfContent);
            response.put("source_file", fileUrl);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "文件处理失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    public static void main(String[] args) {


    }

}
