package org.example.controller;

import org.example.entity.User;
import org.example.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/users")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 获取所有用户列表（仅管理员可访问）
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getAllUsers(HttpSession session) {
        Map<String, Object> response = new HashMap<>();
        
        // 检查是否登录
        User currentUser = (User) session.getAttribute("user");
        if (currentUser == null) {
            response.put("success", false);
            response.put("message", "未登录");
            return ResponseEntity.ok(response);
        }
        
        // 检查是否为管理员
        if (!"Admin".equals(currentUser.getRealname())) {
            response.put("success", false);
            response.put("message", "权限不足，仅管理员可访问");
            return ResponseEntity.ok(response);
        }
        
        try {
            List<User> users = userService.getAllUsers();
            // 不返回密码信息
            List<Map<String, Object>> userList = users.stream()
                .map(this::createUserResponse)
                .collect(Collectors.toList());
            
            response.put("success", true);
            response.put("data", userList);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取用户列表失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 根据ID获取用户信息（仅管理员可访问）
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getUserById(@PathVariable Long id, HttpSession session) {
        Map<String, Object> response = new HashMap<>();
        
        // 检查是否登录
        User currentUser = (User) session.getAttribute("user");
        if (currentUser == null) {
            response.put("success", false);
            response.put("message", "未登录");
            return ResponseEntity.ok(response);
        }
        
        // 检查是否为管理员
        if (!"Admin".equals(currentUser.getRealname())) {
            response.put("success", false);
            response.put("message", "权限不足，仅管理员可访问");
            return ResponseEntity.ok(response);
        }
        
        try {
            Optional<User> userOpt = userService.findById(id);
            if (!userOpt.isPresent()) {
                response.put("success", false);
                response.put("message", "用户不存在");
                return ResponseEntity.ok(response);
            }
            
            response.put("success", true);
            response.put("data", createUserResponse(userOpt.get()));
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取用户信息失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 更新用户信息（仅管理员可访问）
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateUser(@PathVariable Long id, @RequestBody Map<String, String> request, HttpSession session) {
        Map<String, Object> response = new HashMap<>();
        
        // 检查是否登录
        User currentUser = (User) session.getAttribute("user");
        if (currentUser == null) {
            response.put("success", false);
            response.put("message", "未登录");
            return ResponseEntity.ok(response);
        }
        
        // 检查是否为管理员
        if (!"Admin".equals(currentUser.getRealname())) {
            response.put("success", false);
            response.put("message", "权限不足，仅管理员可访问");
            return ResponseEntity.ok(response);
        }
        
        try {
            Optional<User> userOpt = userService.findById(id);
            if (!userOpt.isPresent()) {
                response.put("success", false);
                response.put("message", "用户不存在");
                return ResponseEntity.ok(response);
            }
            
            User user = userOpt.get();
            
            // 更新用户信息
            if (request.containsKey("name")) {
                user.setName(request.get("name"));
            }
            if (request.containsKey("phone")) {
                user.setPhone(request.get("phone"));
            }
            if (request.containsKey("realname")) {
                user.setRealname(request.get("realname"));
            }
            
            User updatedUser = userService.updateUser(user);
            
            response.put("success", true);
            response.put("message", "用户信息更新成功");
            response.put("data", createUserResponse(updatedUser));
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "更新用户信息失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 删除用户（仅管理员可访问）
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteUser(@PathVariable Long id, HttpSession session) {
        Map<String, Object> response = new HashMap<>();
        
        // 检查是否登录
        User currentUser = (User) session.getAttribute("user");
        if (currentUser == null) {
            response.put("success", false);
            response.put("message", "未登录");
            return ResponseEntity.ok(response);
        }
        
        // 检查是否为管理员
        if (!"Admin".equals(currentUser.getRealname())) {
            response.put("success", false);
            response.put("message", "权限不足，仅管理员可访问");
            return ResponseEntity.ok(response);
        }
        
        // 不能删除自己
        if (currentUser.getId().equals(id)) {
            response.put("success", false);
            response.put("message", "不能删除自己的账户");
            return ResponseEntity.ok(response);
        }
        
        try {
            Optional<User> userOpt = userService.findById(id);
            if (!userOpt.isPresent()) {
                response.put("success", false);
                response.put("message", "用户不存在");
                return ResponseEntity.ok(response);
            }
            
            userService.deleteUser(id);
            
            response.put("success", true);
            response.put("message", "用户删除成功");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "删除用户失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 创建用户响应对象（不包含密码）
     */
    private Map<String, Object> createUserResponse(User user) {
        Map<String, Object> userResponse = new HashMap<>();
        userResponse.put("id", user.getId());
        userResponse.put("username", user.getUsername());
        userResponse.put("phone", user.getPhone());
        userResponse.put("name", user.getName());
        userResponse.put("realname", user.getRealname());
        userResponse.put("createTime", user.getCreateTime());
        return userResponse;
    }
}
