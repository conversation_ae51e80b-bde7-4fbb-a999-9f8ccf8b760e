package org.example.repository;

import org.example.model.Rate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RateRepository extends JpaRepository<Rate, Long> {
    @Query("SELECT r FROM Rate r JOIN Route rt ON r.routeId = rt.id WHERE rt.quotationId = :quotationId")
    List<Rate> findByQuotationId(@Param("quotationId") Long quotationId);

    @Query("SELECT r FROM Rate r WHERE r.routeId = :routeId")
    List<Rate> findByRouteId(@Param("routeId") Long routeId);
}
