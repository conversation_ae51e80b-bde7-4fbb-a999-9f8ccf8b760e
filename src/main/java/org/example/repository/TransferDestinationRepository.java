package org.example.repository;

import org.example.model.TransferDestination;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TransferDestinationRepository extends JpaRepository<TransferDestination, Long> {

    @Query("SELECT td FROM TransferDestination td WHERE td.transferId = :transferId")
    List<TransferDestination> findByTransferId(@Param("transferId") Long transferId);

    @Query("SELECT td FROM TransferDestination td WHERE td.toPort = :toPort")
    List<TransferDestination> findByToPort(@Param("toPort") String toPort);
}