package org.example.model;

import javax.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

@Entity
@Table(name = "carriers")
@Data
public class Carrier {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = true, nullable = false)
    private String name;

    @Column(unique = true, nullable = false)
    private String code;

    @Column(length = 500)
    private String address; // 地址

    @Column(length = 50)
    private String phone; // 电话

    @Column(length = 100)
    private String contacts; // 联系人

    @Column(name = "create_time")
    private LocalDateTime createTime; // 创建时间

    @Column(name = "update_time")
    private LocalDateTime updateTime; // 修改时间

    // 在保存前自动设置创建时间
    @PrePersist
    protected void onCreate() {
        createTime = LocalDateTime.now();
        updateTime = LocalDateTime.now();
    }

    // 在更新前自动设置修改时间
    @PreUpdate
    protected void onUpdate() {
        updateTime = LocalDateTime.now();
    }
}
