package org.example.model;

import javax.persistence.*;

@Entity
@Table(name = "freight_calculations")
public class FreightCalculation {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "volume")
    private double volume; // 立方数

    @Column(name = "weight")
    private double weight; // 重量

    @Column(name = "density")
    private double density; // 密度

    @Column(name = "rate_id")
    private Long rateId; // 关联的运价ID

    @Column(name = "calculated_amount")
    private double calculatedAmount; // 计算出的金额

    @Column(name = "quotation_id")
    private Long quotationId; // 关联的报价单ID

    private String origin; // 始发地
    private String destination; // 目的地
    private String carrierCode; // 承运人代码
    private String quoteNumber; // 报价单号

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public double getVolume() {
        return volume;
    }

    public void setVolume(double volume) {
        this.volume = volume;
    }

    public double getWeight() {
        return weight;
    }

    public void setWeight(double weight) {
        this.weight = weight;
    }

    public double getDensity() {
        return density;
    }

    public void setDensity(double density) {
        this.density = density;
    }

    public Long getRateId() {
        return rateId;
    }

    public void setRateId(Long rateId) {
        this.rateId = rateId;
    }

    public double getCalculatedAmount() {
        return calculatedAmount;
    }

    public void setCalculatedAmount(double calculatedAmount) {
        this.calculatedAmount = calculatedAmount;
    }

    public Long getQuotationId() {
        return quotationId;
    }

    public void setQuotationId(Long quotationId) {
        this.quotationId = quotationId;
    }

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public String getCarrierCode() {
        return carrierCode;
    }

    public void setCarrierCode(String carrierCode) {
        this.carrierCode = carrierCode;
    }

    public String getQuoteNumber() {
        return quoteNumber;
    }

    public void setQuoteNumber(String quoteNumber) {
        this.quoteNumber = quoteNumber;
    }

    // 计算密度的方法
    public void calculateDensity() {
        if (volume > 0) {
            this.density = weight / volume;
        }
    }

    // 计算金额的方法
    public void calculateAmount(double rate) {
        this.calculatedAmount = weight * rate;
    }
}
