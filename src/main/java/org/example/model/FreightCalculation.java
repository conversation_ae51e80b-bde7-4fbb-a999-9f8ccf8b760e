package org.example.model;

import javax.persistence.*;

@Entity
@Table(name = "freight_calculations")
public class FreightCalculation {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "volume")
    private double volume; // 立方数

    @Column(name = "weight")
    private double weight; // 重量

    @Column(name = "density")
    private double density; // 密度

    @Column(name = "rate_id")
    private Long rateId; // 关联的运价ID

    @Column(name = "calculated_amount")
    private double calculatedAmount; // 计算出的金额

    @Column(name = "quotation_id")
    private Long quotationId; // 关联的报价单ID

    private String origin; // 始发地
    private String destination; // 目的地
    private String carrierCode; // 承运人代码
    private String quoteNumber; // 报价单号

    // 新增字段，匹配前端显示需求
    private String airline; // 航司
    private double densityLevel; // 密度等级 (kg/m³)
    private double unitPrice; // 单价 (CNY)
    private double chargeWeight; // 计费重量 (kg)
    private double baseAmount; // 基础运费 (CNY)
    private double extraAmount; // 附加费 (CNY)
    private double totalAmount; // 总价 (CNY)
    private String quoteDetailUrl; // 报价单详情链接

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public double getVolume() {
        return volume;
    }

    public void setVolume(double volume) {
        this.volume = volume;
    }

    public double getWeight() {
        return weight;
    }

    public void setWeight(double weight) {
        this.weight = weight;
    }

    public double getDensity() {
        return density;
    }

    public void setDensity(double density) {
        this.density = density;
    }

    public Long getRateId() {
        return rateId;
    }

    public void setRateId(Long rateId) {
        this.rateId = rateId;
    }

    public double getCalculatedAmount() {
        return calculatedAmount;
    }

    public void setCalculatedAmount(double calculatedAmount) {
        this.calculatedAmount = calculatedAmount;
    }

    public Long getQuotationId() {
        return quotationId;
    }

    public void setQuotationId(Long quotationId) {
        this.quotationId = quotationId;
    }

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public String getCarrierCode() {
        return carrierCode;
    }

    public void setCarrierCode(String carrierCode) {
        this.carrierCode = carrierCode;
    }

    public String getQuoteNumber() {
        return quoteNumber;
    }

    public void setQuoteNumber(String quoteNumber) {
        this.quoteNumber = quoteNumber;
    }

    // 新增字段的getter和setter方法
    public String getAirline() {
        return airline;
    }

    public void setAirline(String airline) {
        this.airline = airline;
    }

    public double getDensityLevel() {
        return densityLevel;
    }

    public void setDensityLevel(double densityLevel) {
        this.densityLevel = densityLevel;
    }

    public double getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(double unitPrice) {
        this.unitPrice = unitPrice;
    }

    public double getChargeWeight() {
        return chargeWeight;
    }

    public void setChargeWeight(double chargeWeight) {
        this.chargeWeight = chargeWeight;
    }

    public double getBaseAmount() {
        return baseAmount;
    }

    public void setBaseAmount(double baseAmount) {
        this.baseAmount = baseAmount;
    }

    public double getExtraAmount() {
        return extraAmount;
    }

    public void setExtraAmount(double extraAmount) {
        this.extraAmount = extraAmount;
    }

    public double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getQuoteDetailUrl() {
        return quoteDetailUrl;
    }

    public void setQuoteDetailUrl(String quoteDetailUrl) {
        this.quoteDetailUrl = quoteDetailUrl;
    }

    // 计算密度的方法
    public void calculateDensity() {
        if (volume > 0) {
            this.density = weight / volume;
        }
    }

    // 计算金额的方法（保持向后兼容）
    public void calculateAmount(double rate) {
        this.calculatedAmount = weight * rate;
    }

    // 新的综合计算方法
    public void calculateFreightDetails(double rate, double densityFactor, double mRate, double nRate) {
        // 设置密度等级
        this.densityLevel = densityFactor;

        // 设置单价
        this.unitPrice = rate;

        // 计算计费重量（取体积重量和实际重量的较大值）
        double volumeWeight = volume * densityFactor;
        this.chargeWeight = Math.max(weight, volumeWeight);

        // 计算基础运费
        this.baseAmount = this.chargeWeight * rate;

        // 应用M运价（最低收费）
        if (mRate > 0 && this.baseAmount < mRate) {
            this.baseAmount = mRate;
        }

        // 应用N运价（45KG以下的特殊单价）
        if (nRate > 0 && this.chargeWeight < 45) {
            this.baseAmount = this.chargeWeight * nRate;
        }

        // 附加费暂时设为0，后续可以根据具体业务逻辑计算
        this.extraAmount = 0.0;

        // 计算总价
        this.totalAmount = this.baseAmount + this.extraAmount;

        // 保持向后兼容
        this.calculatedAmount = this.totalAmount;
    }
}
