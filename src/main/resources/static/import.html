<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>运价导入</title>
    <!-- PDF.js库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.16.105/pdf.min.js"></script>
    <script>
        // 设置PDF.js worker路径
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.16.105/pdf.worker.min.js';
    </script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">
    <script src="js/auth.js"></script>
    <style>
        :root {
            --primary-color: #1890ff;
            --border-color: #e8e8e8;
            --bg-color: #f5f7fa;
            --text-color: #333;
            --text-secondary: #666;
        }
        
        body { 
            margin: 0; 
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-color);
            color: var(--text-color);
        }
        
        .nav-bar {
            width: 100%;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 10;
            background: rgba(255,255,255,0.92);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            padding: 16px 0 16px 0;
            display: flex;
            justify-content: center;
        }
        
        .nav-buttons {
            display: flex;
            gap: 12px;
        }
        
        .nav-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s;
            background: #fff;
        }
        
        .nav-btn:hover {
            background: #f5f7fa;
            color: #1890ff;
        }
        
        .nav-btn.primary {
            background: #1890ff;
            color: #fff;
            border-color: #1890ff;
        }
        
        .nav-btn.primary:hover {
            background: #40a9ff;
            color: #fff;
        }
        
        .layout {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            margin-top: 80px !important;
            max-width: 100% !important;
            padding: 0 30px;
        }
        
        .content {
            flex: 1;
            padding: 0 24px 24px;

            margin: 0 auto;
            width: 100%;
        }
        
        .card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            padding: 24px;
            margin-bottom: 24px;
            border: 1px solid var(--border-color);
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--text-color);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .text-area {
            width: 100%;
            height: 300px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 12px;
            font-family: monospace;
            resize: vertical;
            background: #fff;
            color: var(--text-color);
            font-size: 14px;
            line-height: 1.5;
        }
        
        .text-area:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
        }
        
        .button-group {
            display: flex;
            gap: 12px;
            margin: 16px 0;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: #fff;
            color: var(--text-color);
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
        }
        
        .btn:hover {
            background: var(--bg-color);
            color: var(--primary-color);
        }
        
        .btn.primary {
            background: var(--primary-color);
            color: #fff;
            border-color: var(--primary-color);
        }
        
        .btn.primary:hover {
            background: #40a9ff;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .carrier-section {
            display: flex;
            gap: 12px;
            align-items: center;
            margin: 16px 0;
        }
        
        .carrier-select {
            min-width: 200px;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: #fff;
            color: var(--text-color);
        }
        
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            align-items: center;
            justify-content: center;
        }
        
        .modal-content {
            background: #fff;
            border-radius: 8px;
            width: 100%;
            max-width: 500px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .modal-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-color);
        }
        
        .modal-body {
            padding: 24px;
        }
        
        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-color);
        }
        
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: #fff;
            color: var(--text-color);
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 24px;
        }
        
        .spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 3px solid var(--border-color);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .icon {
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="nav-bar">
        <div class="nav-buttons">
            <a href="index.html" class="nav-btn">
                <i class='bx bx-home icon'></i>
                首页
            </a>
            <a href="import.html" class="nav-btn primary">
                <i class='bx bx-import icon'></i>
                导入运价
            </a>
            <a href="list.html" class="nav-btn">
                <i class='bx bx-list-ul icon'></i>
                运价列表
            </a>
            <a href="freight-query.html" class="nav-btn">
                <i class='bx bx-calculator icon'></i>
                运价计算
            </a>
            <a href="carriers.html" class="nav-btn">
                <i class='bx bx-building icon'></i>
                承运商管理
            </a>
        </div>
    </div>
    <div class="layout">
        <div class="content">
            <div class="card">
                <div class="button-group">
                    <button id="upload-btn" class="btn primary" onclick="document.getElementById('file-input').click();">
                        <i class='bx bx-upload icon'></i>
                        浏览文件
                    </button>
                    <input type="file" id="file-input" accept=".pdf" style="display: none" onchange="handleFileUpload(event)">
                    <span id="file-name" style="margin-left: 10px; color: var(--text-secondary);"></span>
                </div>

                <div class="card-title">
                    <i class='bx bx-file-pdf icon'></i>
                    PDF文本内容
                </div>
                <textarea id="pdf-content" class="text-area" readonly></textarea>

                <div class="button-group">
                    <button id="analyze-btn" class="btn primary" disabled>
                        <i class='bx bx-analyse icon'></i>
                        LLM分析
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-title">
                    <i class='bx bx-code-alt icon'></i>
                    解析结果
                </div>
                <textarea id="analysis-result" class="text-area" readonly></textarea>
            </div>

            <div class="card">
                <div class="card-title">
                    <i class='bx bx-building icon'></i>
                    承运商信息
                </div>
                <div class="carrier-section">
                    <select id="carrier-select" class="carrier-select">
                        <option value="">请选择承运商</option>
                    </select>
                    <small class="text-muted">请从列表中选择承运商，如需新增请前往<a href="carriers.html" target="_blank">承运商管理</a></small>
                </div>

                <div class="button-group">
                    <button id="import-btn" class="btn primary" disabled>
                        <i class='bx bx-import icon'></i>
                        导入运价
                    </button>
                </div>
            </div>



            <div id="loading" class="loading">
                <div class="spinner"></div>
                <div style="margin-top: 16px; color: var(--text-secondary);">正在处理中...</div>
            </div>

            <input type="hidden" id="source-file" />
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/jquery@3.7.1/dist/jquery.min.js"></script>
    <script>
        let pdfContent = '';
        let parsedJson = null;

        // 文件上传处理函数
        async function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            // 显示文件名
            document.getElementById('file-name').textContent = file.name;

            try {
                // 显示加载状态
                document.getElementById('loading').style.display = 'block';

                // 创建 FormData 对象
                const formData = new FormData();
                formData.append('file', file);

                // 发送文件上传请求
                const response = await fetch('/api/quotations/upload', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.message || `上传失败: ${response.status}`);
                }

                const result = await response.json();

                if (result.success) {
                    // 显示提取的文本内容
                    document.getElementById('pdf-content').value = result.content;
                    // 存储源文件路径
                    document.getElementById('source-file').value = result.source_file;
                    // 启用分析按钮
                    document.getElementById('analyze-btn').disabled = false;
                    pdfContent = result.content;
                    alert('PDF文本提取成功');
                } else {
                    throw new Error(result.message || '文本提取失败');
                }
            } catch (error) {
                console.error('上传处理失败:', error);
                document.getElementById('pdf-content').value = '';
                alert(error.message || '上传处理失败');
            } finally {
                // 隐藏加载状态
                document.getElementById('loading').style.display = 'none';
            }
        }

        // LLM分析
        async function analyzePdfContent() {
            if (!pdfContent) {
                alert('请先上传PDF文件');
                return;
            }

            try {
                showLoading(true);
                document.getElementById('analyze-btn').disabled = true;

                const response = await fetch('/api/quotations/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ content: pdfContent })
                });

                const result = await response.json();
                if (!result.success) {
                    throw new Error(result.message || '解析失败');
                }

                parsedJson = result.data;
                document.getElementById('analysis-result').value = JSON.stringify(parsedJson, null, 2);
                validateImportButton();
                alert('LLM解析成功');

            } catch (error) {
                console.error('LLM解析失败:', error);
                alert(error.message || 'LLM解析失败，请稍后重试');
            } finally {
                showLoading(false);
                document.getElementById('analyze-btn').disabled = false;
            }
        }

        document.getElementById('analyze-btn').onclick = analyzePdfContent;

        // 承运商相关
        function loadCarriers() {
            fetch('/api/quotations/carriers')
                .then(res => res.json())
                .then(list => {
                    const select = document.getElementById('carrier-select');
                    select.innerHTML = '<option value="" data-code="">请选择承运商</option>';
                    list.forEach(c => {
                        const opt = document.createElement('option');
                        opt.value = c.id;
                        opt.setAttribute('data-code', c.code);  // 存储承运商代码
                        opt.text = c.name + ' (' + c.code + ')';
                        select.appendChild(opt);
                    });
                });
        }



        // 导入运价
        document.getElementById('import-btn').onclick = async function() {
            const carrierSelect = document.getElementById('carrier-select');
            const selectedOption = carrierSelect.options[carrierSelect.selectedIndex];

            if (!selectedOption.value) {
                alert('请选择承运商');
                return;
            }

            if (!parsedJson) {
                alert('请先进行LLM分析');
                return;
            }

            try {
                showLoading(true);
                document.getElementById('import-btn').disabled = true;

                const response = await fetch('/api/quotations/import', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        data: parsedJson,
                        source_file: document.getElementById('source-file').value,
                        carrier_code: selectedOption.getAttribute('data-code')  // 传递承运商代码
                    })
                });

                const result = await response.json();
                if (!result.success) {
                    throw new Error(result.message || '导入失败');
                }

                alert('导入成功');
                window.location.href = 'list.html';
            } catch (error) {
                console.error('导入失败:', error);
                alert(error.message || '导入失败，请稍后重试');
            } finally {
                showLoading(false);
                document.getElementById('import-btn').disabled = false;
            }
        };

        // 工具函数
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function validateImportButton() {
            const carrier = document.getElementById('carrier-select').value;
            document.getElementById('import-btn').disabled = !(carrier && parsedJson);
        }

        document.getElementById('carrier-select').onchange = validateImportButton;

        // 初始化
        loadCarriers();
    </script>
</body>
</html>
