<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>运价查询</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">
    <style>
        body {
            background: #f5f7fa;
        }
        .nav-buttons {
            display: flex;
            gap: 12px;
            margin-bottom: 24px;
        }
        .nav-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s;
            background: #fff;
        }
        .nav-btn:hover {
            background: #f5f7fa;
            color: #1890ff;
        }
        .nav-btn.primary {
            background: #1890ff;
            color: #fff;
            border-color: #1890ff;
        }
        .nav-btn.primary:hover {
            background: #40a9ff;
            color: #fff;
        }
        .icon {
            font-size: 18px;
        }
        .card-center {
            max-width: 700px;
            margin: 40px auto 0 auto;
            box-shadow: 0 4px 24px rgba(0,0,0,0.08);
            border-radius: 16px;
            background: #fff;
            padding: 32px 32px 24px 32px;
        }
        .form-label {
            font-weight: 500;
        }
        .form-control, .btn {
            border-radius: 8px;
        }
        .btn-primary {
            box-shadow: 0 2px 8px rgba(24,144,255,0.08);
        }
        .table {
            border-radius: 12px;
            overflow: hidden;
            margin-top: 24px;
        }
        .table-striped > tbody > tr:nth-of-type(odd) {
            background-color: #f8fafc;
        }
        .table-hover tbody tr:hover {
            background-color: #e6f7ff;
        }
        .no-data {
            text-align: center;
            color: #999;
            padding: 32px 0 0 0;
            font-size: 16px;
        }
        .title-bar {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 24px;
        }
        .title-bar .icon {
            color: #1890ff;
            font-size: 28px;
        }
    </style>
</head>
<body>
<div class="container mt-4">
    <div class="nav-buttons">
        <a href="index.html" class="nav-btn">
            <i class='bx bx-home icon'></i>
            首页
        </a>
        <a href="import.html" class="nav-btn">
            <i class='bx bx-import icon'></i>
            导入运价
        </a>
        <a href="list.html" class="nav-btn">
            <i class='bx bx-list-ul icon'></i>
            运价列表
        </a>
        <a href="freight-query.html" class="nav-btn primary">
            <i class='bx bx-calculator icon'></i>
            运价计算
        </a>
    </div>
    <div class="card-center">
        <div class="title-bar">
            <i class='bx bx-search-alt icon'></i>
            <h3 class="mb-0">运价查询</h3>
        </div>
        <form id="queryForm" class="row g-3 mb-2">
            <div class="col-md-6">
                <label for="volume" class="form-label">体积 (m³)</label>
                <input type="number" step="0.01" class="form-control" id="volume" name="volume" required>
            </div>
            <div class="col-md-6">
                <label for="weight" class="form-label">重量 (kg)</label>
                <input type="number" step="0.01" class="form-control" id="weight" name="weight" required>
            </div>
            <div class="col-12 d-flex justify-content-end">
                <button type="submit" class="btn btn-primary px-4">查询运价</button>
            </div>
        </form>
        <div id="resultHeader" style="display:none;" class="mb-3">
            <span class="badge bg-primary" id="routeLabel"></span>
            <span class="badge bg-info" id="weightLabel"></span>
            <span class="badge bg-info" id="volumeLabel"></span>
        </div>
        <div id="resultCards" class="row g-3"></div>
        <div class="no-data" id="noData" style="display:none;">
            <i class='bx bx-info-circle'></i> 暂无数据，请输入参数后查询。
        </div>
    </div>
</div>
<script>
    document.getElementById('queryForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const volume = document.getElementById('volume').value;
        const weight = document.getElementById('weight').value;
        fetch(`/api/freight-query/calculate?volume=${volume}&weight=${weight}`)
            .then(res => res.json())
            .then(data => {
                const header = document.getElementById('resultHeader');
                const cards = document.getElementById('resultCards');
                cards.innerHTML = '';
                if (data.length > 0) {
                    header.style.display = '';
                    document.getElementById('routeLabel').innerText = `${data[0].origin} → ${data[0].destination}`;
                    document.getElementById('weightLabel').innerText = `${data[0].chargeWeight} kg`;
                    document.getElementById('volumeLabel').innerText = `${volume} m³`;
                    data.forEach(item => {
                        cards.innerHTML += `
                        <div class="col-md-6">
                          <div class="card shadow-sm">
                            <div class="card-body">
                              <h5 class="card-title">${item.origin} → ${item.destination} <span class="badge bg-secondary">${item.carrierCode || '-'}</span></h5>
                              <ul class="list-unstyled mb-2">
                                <li>承运商: ${item.carrierCode || '-'}</li>
                                <li>航司: ${item.airline || '-'}</li>
                                <li>密度等级: ${item.densityLevel} kg/m³</li>
                                <li>单价: ${item.unitPrice.toFixed(2)} CNY</li>
                                <li>计费重量: ${item.chargeWeight} kg</li>
                                <li>基础运费: ${item.baseAmount.toFixed(2)} CNY</li>
                                <li>附加费: ${item.extraAmount.toFixed(2)} CNY</li>
                                <li><b>总价: ${item.totalAmount.toFixed(2)} CNY</b></li>
                              </ul>
                              <a href="${item.quoteDetailUrl}" class="card-link" target="_blank">报价单号: ${item.quoteNumber || '-'}</a>
                            </div>
                          </div>
                        </div>
                        `;
                    });
                    document.getElementById('noData').style.display = 'none';
                } else {
                    header.style.display = 'none';
                    document.getElementById('noData').style.display = '';
                }
            });
    });
</script>
</body>
</html>
