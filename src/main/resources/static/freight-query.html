<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>运价查询</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">
    <style>
        body {
            background: #f5f7fa;
            margin: 0;
        }
        .nav-bar {
            width: 100%;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 10;
            background: rgba(255,255,255,0.92);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            padding: 16px 0 16px 0;
            display: flex;
            justify-content: center;
        }
        .nav-buttons {
            display: flex;
            gap: 12px;
        }
        .nav-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s;
            background: #fff;
        }
        .nav-btn:hover {
            background: #f5f7fa;
            color: #1890ff;
        }
        .nav-btn.primary {
            background: #1890ff;
            color: #fff;
            border-color: #1890ff;
        }
        .nav-btn.primary:hover {
            background: #40a9ff;
            color: #fff;
        }
        .icon {
            font-size: 18px;
        }
        .container, .content {
            margin-top: 80px !important;
            max-width: 100% !important;
            padding: 0 30px;
        }
        .card-center {
            max-width: 700px;
            margin: 40px auto 0 auto;
            box-shadow: 0 4px 24px rgba(0,0,0,0.08);
            border-radius: 16px;
            background: #fff;
            padding: 32px 32px 24px 32px;
        }
        .form-label {
            font-weight: 500;
        }
        .form-control, .btn {
            border-radius: 8px;
        }
        .btn-primary {
            box-shadow: 0 2px 8px rgba(24,144,255,0.08);
        }
        .table {
            border-radius: 12px;
            overflow: hidden;
            margin-top: 24px;
        }
        .table-striped > tbody > tr:nth-of-type(odd) {
            background-color: #f8fafc;
        }
        .table-hover tbody tr:hover {
            background-color: #e6f7ff;
        }
        .no-data {
            text-align: center;
            color: #999;
            padding: 32px 0 0 0;
            font-size: 16px;
        }
        .title-bar {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 24px;
        }
        .title-bar .icon {
            color: #1890ff;
            font-size: 28px;
        }
        .card-header {
            border-bottom: 1px solid #e8e8e8;
        }
        .result-card {
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .result-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.12);
        }
        .price-highlight {
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body>
<div class="nav-bar">
    <div class="nav-buttons">
        <a href="index.html" class="nav-btn">
            <i class='bx bx-home icon'></i>
            首页
        </a>
        <a href="import.html" class="nav-btn">
            <i class='bx bx-import icon'></i>
            导入运价
        </a>
        <a href="list.html" class="nav-btn">
            <i class='bx bx-list-ul icon'></i>
            运价列表
        </a>
        <a href="freight-query.html" class="nav-btn primary">
            <i class='bx bx-calculator icon'></i>
            运价计算
        </a>
        <a href="carriers.html" class="nav-btn">
            <i class='bx bx-building icon'></i>
            承运商管理
        </a>
    </div>
</div>
<div class="container mt-4">
    <div class="content">
        <!-- 查询条件卡片开始 -->
        <div class="card mb-4 shadow-sm">
            <div class="card-header bg-white border-bottom-0">
                <div class="title-bar mb-0">
                    <i class='bx bx-search-alt icon'></i>
                    <h3 class="mb-0">运价查询</h3>
                </div>
            </div>
            <div class="card-body pb-2">
                <form id="queryForm" class="row g-3 mb-2">
                    <div class="col-md-6">
                        <label for="origin" class="form-label">起运港 *</label>
                        <input type="text" class="form-control" id="origin" name="origin" placeholder="如：PEK" required list="originList">
                        <datalist id="originList"></datalist>
                    </div>
                    <div class="col-md-6">
                        <label for="destination" class="form-label">目的港 *</label>
                        <input type="text" class="form-control" id="destination" name="destination" placeholder="如：FRA" required list="destinationList">
                        <datalist id="destinationList"></datalist>
                    </div>
                    <div class="col-md-6">
                        <label for="weight" class="form-label">重量 (kg) *</label>
                        <input type="number" step="0.01" class="form-control" id="weight" name="weight" placeholder="如：100.0" required>
                    </div>
                    <div class="col-md-6">
                        <label for="volume" class="form-label">体积 (m³) *</label>
                        <input type="number" step="0.01" class="form-control" id="volume" name="volume" placeholder="如：1.5" required>
                    </div>
                    <div class="col-12">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeTransfer" name="includeTransfer">
                            <label class="form-check-label" for="includeTransfer">
                                <i class='bx bx-transfer'></i> 包含中转方案
                            </label>
                            <small class="text-muted d-block">勾选后将显示中转运输方案和费用</small>
                        </div>
                    </div>
                    <div class="col-12 d-flex justify-content-between align-items-center">
                        <div class="text-muted small">
                            <i class='bx bx-info-circle'></i> 请输入准确的起运港和目的港代码以获得精确报价
                        </div>
                        <button type="submit" class="btn btn-primary px-4">
                            <i class='bx bx-search'></i> 查询运价
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <!-- 查询条件卡片结束 -->

        <!-- 查询结果卡片开始 -->
        <div class="card shadow-sm">
            <div class="card-body">
                <div id="resultHeader" style="display:none;" class="mb-3">
                    <div class="d-flex flex-wrap gap-2">
                        <span class="badge bg-primary fs-6" id="routeLabel"></span>
                        <span class="badge bg-info fs-6" id="weightLabel"></span>
                        <span class="badge bg-info fs-6" id="volumeLabel"></span>
                        <span class="badge bg-success fs-6" id="resultCountLabel"></span>
                    </div>
                </div>
                <div id="resultCards" class="row g-3"></div>
                <div class="no-data" id="noData" style="display:none;">
                    <i class='bx bx-info-circle'></i> 暂无数据，请输入参数后查询。
                </div>
            </div>
        </div>
        <!-- 查询结果卡片结束 -->
    </div>
</div>
<script>
    document.getElementById('queryForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const origin = document.getElementById('origin').value;
        const destination = document.getElementById('destination').value;
        const volume = document.getElementById('volume').value;
        const weight = document.getElementById('weight').value;
        const includeTransfer = document.getElementById('includeTransfer').checked;

        // 构建查询参数
        const params = new URLSearchParams({
            volume: volume,
            weight: weight,
            origin: origin,
            destination: destination
        });

        // 根据是否勾选中转决定查询策略
        if (includeTransfer) {
            // 同时查询直达和中转方案
            Promise.all([
                fetch(`/api/freight-query/calculate?${params}`).then(res => res.json()),
                fetch(`/api/freight-query/calculate-transfer?${params}`).then(res => res.json())
            ]).then(([directData, transferData]) => {
                handleCombinedResults(directData, transferData, origin, destination, weight, volume, includeTransfer);
            }).catch(error => {
                handleError(error, origin, destination, includeTransfer);
            });
        } else {
            // 只查询直达方案
            fetch(`/api/freight-query/calculate?${params}`)
                .then(res => res.json())
                .then(data => {
                    handleDirectResults(data, origin, destination, weight, volume, includeTransfer);
                }).catch(error => {
                    handleError(error, origin, destination, includeTransfer);
                });
        }
    });

    // 处理直达结果
    function handleDirectResults(data, origin, destination, weight, volume, includeTransfer) {
        const header = document.getElementById('resultHeader');
        const cards = document.getElementById('resultCards');
        cards.innerHTML = '';
        if (data.length > 0) {
            header.style.display = '';
            document.getElementById('routeLabel').innerText = `${origin} → ${destination}`;
            document.getElementById('weightLabel').innerText = `${weight} kg`;
            document.getElementById('volumeLabel').innerText = `${volume} m³`;
            document.getElementById('resultCountLabel').innerText = `找到${data.length}条报价`;

            // 显示直达方案
            data.forEach((item, index) => {
                cards.innerHTML += generateDirectCard(item);
            });
            document.getElementById('noData').style.display = 'none';
        } else {
            header.style.display = 'none';
            const messageType = includeTransfer ? '运价信息' : '运价信息';
            document.getElementById('noData').innerHTML = `
                <i class='bx bx-search'></i>
                未找到从 ${origin} 到 ${destination} 的${messageType}，请检查起运港和目的港代码是否正确。
            `;
            document.getElementById('noData').style.display = '';
        }
    }

    // 处理合并结果（直达 + 中转）
    function handleCombinedResults(directData, transferData, origin, destination, weight, volume, includeTransfer) {
        const header = document.getElementById('resultHeader');
        const cards = document.getElementById('resultCards');
        cards.innerHTML = '';

        const totalResults = directData.length + transferData.length;

        if (totalResults > 0) {
            header.style.display = '';
            document.getElementById('routeLabel').innerText = `${origin} → ${destination}`;
            document.getElementById('weightLabel').innerText = `${weight} kg`;
            document.getElementById('volumeLabel').innerText = `${volume} m³`;
            document.getElementById('resultCountLabel').innerText = `找到${directData.length}条直达 + ${transferData.length}条中转方案`;

            // 先显示直达方案
            if (directData.length > 0) {
                cards.innerHTML += `
                    <div class="col-12 mb-3">
                        <h5 class="text-primary">
                            <i class='bx bx-plane-takeoff'></i> 直达方案 (${directData.length}条)
                        </h5>
                    </div>
                `;
                directData.forEach((item, index) => {
                    cards.innerHTML += generateDirectCard(item);
                });
            }

            // 再显示中转方案
            if (transferData.length > 0) {
                cards.innerHTML += `
                    <div class="col-12 mb-3 mt-4">
                        <h5 class="text-warning">
                            <i class='bx bx-transfer'></i> 中转方案 (${transferData.length}条)
                        </h5>
                    </div>
                `;
                transferData.forEach((item, index) => {
                    cards.innerHTML += generateTransferCard(item);
                });
            }

            document.getElementById('noData').style.display = 'none';
        } else {
            header.style.display = 'none';
            document.getElementById('noData').innerHTML = `
                <i class='bx bx-search'></i>
                未找到从 ${origin} 到 ${destination} 的运价信息和中转方案，请检查起运港和目的港代码是否正确。
            `;
            document.getElementById('noData').style.display = '';
        }
    }

    // 生成直达方案卡片
    function generateDirectCard(item) {
        return `
            <div class="col-md-6 mb-3">
              <div class="card shadow-sm h-100" style="border-left: 4px solid #1890ff;">
                <div class="card-header bg-light">
                  <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class='bx bx-plane-takeoff'></i> ${item.origin} → ${item.destination}
                    </h6>
                    <span class="badge bg-secondary">${item.carrierCode || 'BIZK'}</span>
                  </div>
                </div>
                <div class="card-body">
                  <div class="row g-2 mb-3">
                    <div class="col-6">
                      <small class="text-muted">承运商</small>
                      <div>${item.carrierCode || 'BIZK'}</div>
                    </div>
                    <div class="col-6">
                      <small class="text-muted">航司</small>
                      <div>${item.airline || 'LH'}</div>
                    </div>
                    <div class="col-6">
                      <small class="text-muted">密度等级</small>
                      <div>${item.densityLevel.toFixed(0)} kg/m³</div>
                    </div>
                    <div class="col-6">
                      <small class="text-muted">单价</small>
                      <div>${item.unitPrice.toFixed(2)} CNY</div>
                    </div>
                  </div>
                  <hr>
                  <div class="row g-2">
                    <div class="col-6">
                      <small class="text-muted">计费重量</small>
                      <div class="fw-bold">${item.chargeWeight.toFixed(1)} kg</div>
                    </div>
                    <div class="col-6">
                      <small class="text-muted">基础运费</small>
                      <div>${item.baseAmount.toFixed(2)} CNY</div>
                    </div>
                    <div class="col-6">
                      <small class="text-muted">附加费</small>
                      <div>${item.extraAmount.toFixed(2)} CNY</div>
                    </div>
                    <div class="col-6">
                      <small class="text-muted">总价</small>
                      <div class="fw-bold text-primary fs-5">${item.totalAmount.toFixed(2)} CNY</div>
                    </div>
                  </div>
                </div>
                <div class="card-footer bg-light">
                  <a href="${item.quoteDetailUrl}" class="btn btn-outline-primary btn-sm" target="_blank">
                    <i class='bx bx-link-external'></i> 报价单号: ${item.quoteNumber || 'LH2024030821'}
                  </a>
                </div>
              </div>
            </div>
        `;
    }

    // 生成中转方案卡片
    function generateTransferCard(item) {
        return `
            <div class="col-12 mb-3">
              <div class="card shadow-sm h-100" style="border-left: 4px solid #ff6b35;">
                <div class="card-header bg-warning bg-opacity-10">
                  <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class='bx bx-transfer'></i> 中转方案: ${item.firstSegmentOrigin} → ${item.firstSegmentDestination} → ${item.secondSegmentDestination}
                    </h6>
                    <span class="badge bg-warning">${item.firstCarrierCode || 'BIZK'}</span>
                  </div>
                </div>
                <div class="card-body">
                  <!-- 第一段运输 -->
                  <div class="row mb-4">
                    <div class="col-12">
                      <h6 class="text-primary mb-3">
                        <i class='bx bx-plane-takeoff'></i> 第一段: ${item.firstSegmentOrigin} → ${item.firstSegmentDestination}
                      </h6>
                    </div>
                    <div class="col-md-3">
                      <small class="text-muted">承运商</small>
                      <div>${item.firstCarrierCode || 'BIZK'}</div>
                    </div>
                    <div class="col-md-3">
                      <small class="text-muted">航司</small>
                      <div>${item.firstAirline || 'LH'}</div>
                    </div>
                    <div class="col-md-3">
                      <small class="text-muted">密度等级</small>
                      <div>${item.firstDensityLevel.toFixed(0)} kg/m³</div>
                    </div>
                    <div class="col-md-3">
                      <small class="text-muted">单价</small>
                      <div>${item.firstUnitPrice.toFixed(2)} CNY</div>
                    </div>
                    <div class="col-md-3 mt-2">
                      <small class="text-muted">计费重量</small>
                      <div class="fw-bold">${item.firstChargeWeight.toFixed(1)} kg</div>
                    </div>
                    <div class="col-md-3 mt-2">
                      <small class="text-muted">基础运费</small>
                      <div>${item.firstBaseAmount.toFixed(2)} CNY</div>
                    </div>
                    <div class="col-md-3 mt-2">
                      <small class="text-muted">附加费</small>
                      <div>${item.firstExtraAmount.toFixed(2)} CNY</div>
                    </div>
                    <div class="col-md-3 mt-2">
                      <small class="text-muted">第一段小计</small>
                      <div class="fw-bold text-success">${item.firstTotalAmount.toFixed(2)} CNY</div>
                    </div>
                  </div>

                  <!-- 第二段中转 -->
                  <div class="row mb-3">
                    <div class="col-12">
                      <h6 class="text-info mb-3">
                        <i class='bx bx-transfer-alt'></i> 第二段中转: ${item.secondSegmentOrigin} → ${item.secondSegmentDestination}
                      </h6>
                    </div>
                    <div class="col-md-4">
                      <small class="text-muted">中转费用单价</small>
                      <div>${item.transferCostPerKg.toFixed(2)} ${item.transferCurrency}/kg</div>
                    </div>
                    <div class="col-md-4">
                      <small class="text-muted">重量</small>
                      <div>${item.totalWeight.toFixed(1)} kg</div>
                    </div>
                    <div class="col-md-4">
                      <small class="text-muted">中转费用小计</small>
                      <div class="fw-bold text-info">${item.transferTotalAmount.toFixed(2)} ${item.transferCurrency}</div>
                    </div>
                  </div>

                  <!-- 总计 -->
                  <hr>
                  <div class="row">
                    <div class="col-md-8">
                      <h6 class="mb-0">总计费用</h6>
                      <small class="text-muted">第一段运费 + 中转费用</small>
                    </div>
                    <div class="col-md-4 text-end">
                      <div class="fw-bold text-primary fs-4">${item.grandTotalAmount.toFixed(2)} CNY</div>
                    </div>
                  </div>
                </div>
                <div class="card-footer bg-light">
                  <a href="${item.quoteDetailUrl}" class="btn btn-outline-primary btn-sm" target="_blank">
                    <i class='bx bx-link-external'></i> 报价单号: ${item.quoteNumber || 'LH2024030821'}
                  </a>
                </div>
              </div>
            </div>
        `;
    }

    // 处理错误
    function handleError(error, origin, destination, includeTransfer) {
        console.error('查询失败:', error);
        document.getElementById('resultHeader').style.display = 'none';
        document.getElementById('resultCards').innerHTML = '';
        document.getElementById('noData').innerHTML = `
            <i class='bx bx-error'></i>
            查询失败，请稍后重试。
        `;
        document.getElementById('noData').style.display = '';
    }

    // 页面加载时获取可用港口列表
    document.addEventListener('DOMContentLoaded', function() {
        fetch('/api/freight-query/ports')
            .then(res => res.json())
            .then(data => {
                const originList = document.getElementById('originList');
                const destinationList = document.getElementById('destinationList');

                // 填充起运港选项
                data.origins.forEach(port => {
                    const option = document.createElement('option');
                    option.value = port;
                    originList.appendChild(option);
                });

                // 填充目的港选项
                data.destinations.forEach(port => {
                    const option = document.createElement('option');
                    option.value = port;
                    destinationList.appendChild(option);
                });
            })
            .catch(error => {
                console.error('获取港口列表失败:', error);
            });
    });
</script>
</body>
</html>
