// 初始化PDF.js
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.12.313/pdf.worker.min.js';

let currentPdf = null;
let currentPage = 1;
let pageCount = 1;
let currentScale = 1.0;

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 上传按钮事件
    document.getElementById('upload-btn').addEventListener('click', function() {
        document.getElementById('file-input').click();
    });

    // 文件选择事件
    document.getElementById('file-input').addEventListener('change', handleFileUpload);

    // PDF控制按钮事件
    document.getElementById('prev-page').addEventListener('click', showPrevPage);
    document.getElementById('next-page').addEventListener('click', showNextPage);
    document.getElementById('zoom-in').addEventListener('click', () => changeScale(0.1));
    document.getElementById('zoom-out').addEventListener('click', () => changeScale(-0.1));

    // 导入运价按钮事件
    document.getElementById('import-quotation-btn').addEventListener('click', function() {
        document.getElementById('quotation-input').click();
    });

    // 运价文件选择事件
    document.getElementById('quotation-input').addEventListener('change', handleQuotationImport);
});

// 处理文件上传
async function handleFileUpload(event) {
    const file = event.target.files[0];
    if (!file || file.type !== 'application/pdf') {
        antd.message.error('请选择PDF文件');
        return;
    }

    try {
        // 显示加载中
        showLoading(true);

        // 加载PDF预览
        const arrayBuffer = await file.arrayBuffer();
        await loadPdfPreview(arrayBuffer);

        // 上传文件到服务器
        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch('/api/quotations/upload', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error('上传失败');
        }

        const result = await response.json();
        displayResult(result);

        // 显示PDF控制器和导出按钮
        document.getElementById('pdf-controls').style.display = 'block';
        document.getElementById('export-btn').style.display = 'block';

    } catch (error) {
        antd.message.error('处理失败: ' + error.message);
    } finally {
        showLoading(false);
    }
}

// 加载PDF预览
async function loadPdfPreview(arrayBuffer) {
    try {
        // 加载PDF文档
        currentPdf = await pdfjsLib.getDocument({data: arrayBuffer}).promise;
        pageCount = currentPdf.numPages;
        currentPage = 1;

        // 更新页码信息
        updatePageInfo();

        // 显示第一页
        await renderPage(currentPage);
    } catch (error) {
        console.error('PDF加载失败:', error);
        throw error;
    }
}

// 渲染PDF页面
async function renderPage(pageNumber) {
    try {
        const page = await currentPdf.getPage(pageNumber);
        const viewport = page.getViewport({scale: currentScale});

        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.height = viewport.height;
        canvas.width = viewport.width;

        const canvasContainer = document.getElementById('canvas-container');
        canvasContainer.innerHTML = '';
        canvasContainer.appendChild(canvas);

        await page.render({
            canvasContext: context,
            viewport: viewport
        }).promise;
    } catch (error) {
        console.error('页面渲染失败:', error);
        antd.message.error('页面渲染失败');
    }
}

// 显示上一页
async function showPrevPage() {
    if (currentPage > 1) {
        currentPage--;
        await renderPage(currentPage);
        updatePageInfo();
    }
}

// 显示下一页
async function showNextPage() {
    if (currentPage < pageCount) {
        currentPage++;
        await renderPage(currentPage);
        updatePageInfo();
    }
}

// 更改缩放比例
async function changeScale(delta) {
    currentScale = Math.max(0.5, Math.min(2.0, currentScale + delta));
    await renderPage(currentPage);
}

// 更新页码信息
function updatePageInfo() {
    document.getElementById('page-info').textContent = `第 ${currentPage} 页 / 共 ${pageCount} 页`;
}

// 显示/隐藏加载提示
function showLoading(show) {
    document.getElementById('loading').style.display = show ? 'block' : 'none';
}

// 显示解析结果
function displayResult(data) {
    const resultTable = document.getElementById('result-table');

    // 创建表格显示解析结果
    if (data.parseResult) {
        try {
            const jsonData = typeof data.parseResult === 'string' ?
                JSON.parse(data.parseResult) : data.parseResult;

            // 创建数据表格
            createTable(jsonData);
        } catch (error) {
            console.error('解析JSON失败:', error);
            resultTable.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        }
    } else {
        resultTable.innerHTML = '<div class="ant-empty"><div class="ant-empty-image"></div><p>暂无数据</p></div>';
    }
}

// 创建数据表格
function createTable(jsonData) {
    const resultTable = document.getElementById('result-table');
    resultTable.innerHTML = '';

    // 如果存在routes数据，创建路由表格
    if (jsonData.routes && jsonData.routes.length > 0) {
        const table = document.createElement('table');
        table.className = 'ant-table-wrapper';

        // 创建表头
        const thead = document.createElement('thead');
        thead.innerHTML = `
            <tr>
                <th>起运港</th>
                <th>目的港</th>
                <th>航空公司</th>
                <th>M-rate</th>
                <th>N-rate</th>
                <th>操作</th>
            </tr>
        `;
        table.appendChild(thead);

        // 创建表体
        const tbody = document.createElement('tbody');
        jsonData.routes.forEach(route => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${route.origin || '-'}</td>
                <td>${route.destination || '-'}</td>
                <td>${route.carrier_code || '-'}</td>
                <td>${route['M-rate'] || '-'}</td>
                <td>${route['N-rate'] || '-'}</td>
                <td>
                    <button class="ant-btn ant-btn-link" onclick="showRateDetails('${escape(JSON.stringify(route))}')">
                        查看费率
                    </button>
                </td>
            `;
            tbody.appendChild(tr);
        });
        table.appendChild(tbody);
        resultTable.appendChild(table);
    } else {
        resultTable.innerHTML = '<div class="ant-empty"><div class="ant-empty-image"></div><p>暂无路由数据</p></div>';
    }
}

// 显示费率详情
function showRateDetails(routeJson) {
    const route = JSON.parse(unescape(routeJson));
    if (route.rates && route.rates.length > 0) {
        const content = `
            <table class="ant-table-wrapper" style="width:100%">
                <thead>
                    <tr>
                        <th>最小重量</th>
                        <th>最大重量</th>
                        <th>价格</th>
                        <th>密度类型</th>
                        <th>密度系数</th>
                    </tr>
                </thead>
                <tbody>
                    ${route.rates.map(rate => `
                        <tr>
                            <td>${rate.min_wt}</td>
                            <td>${rate.max_wt}</td>
                            <td>${rate.price}</td>
                            <td>${rate.density_type}</td>
                            <td>${rate.density_factor || '-'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;

        antd.Modal.info({
            title: '费率详情',
            width: 800,
            content: content,
            maskClosable: true
        });
    } else {
        antd.message.info('该路由暂无费率数据');
    }
}

// 数据导出功能
document.getElementById('export-btn').addEventListener('click', function() {
    const resultTable = document.getElementById('result-table');
    const data = resultTable.innerHTML;

    // 创建一个Blob对象
    const blob = new Blob([`
        <html>
            <head>
                <meta charset="utf-8">
                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/antd/dist/antd.min.css">
            </head>
            <body>
                ${data}
            </body>
        </html>
    `], {type: 'text/html'});

    // 创建下载链接
    const a = document.createElement('a');
    a.href = URL.createObjectURL(blob);
    a.download = '解析结果_' + new Date().toISOString().slice(0,10) + '.html';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
});

// 处理运价导入
async function handleQuotationImport(event) {
    const file = event.target.files[0];
    if (!file || !file.name.match(/\.(xlsx|xls)$/)) {
        antd.message.error('请选择Excel文件(.xlsx或.xls)');
        return;
    }

    try {
        showLoading(true);
        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch('/api/quotations/import', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error('导入失败');
        }

        const result = await response.json();

        if (result.success) {
            antd.message.success('运价导入成功');
            // 刷新数据显示
            displayResult(result.data);
        } else {
            antd.message.error(result.message || '导入失败');
        }

    } catch (error) {
        antd.message.error('导入失败: ' + error.message);
    } finally {
        showLoading(false);
        // 清空文件输入框，允许重复选择相同文件
        event.target.value = '';
    }
}
