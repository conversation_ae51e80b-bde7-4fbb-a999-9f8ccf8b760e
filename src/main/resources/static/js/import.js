// 初始化PDF.js
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.12.313/pdf.worker.min.js';

let currentPdf = null;
let currentPage = 1;
let pageCount = 1;
let currentScale = 1.0;
let pdfContent = '';
let parsedJson = null;
let selectedCarrierId = null;

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化承运商下拉列表
    loadCarriers();

    // 上传按钮事件
    document.getElementById('upload-btn').addEventListener('click', function() {
        document.getElementById('file-input').click();
    });

    // 文件选择事件
    document.getElementById('file-input').addEventListener('change', handleFileUpload);

    // LLM分析按钮事件
    document.getElementById('analyze-btn').addEventListener('click', analyzePdfContent);

    // 导入运价按钮事件
    document.getElementById('import-btn').addEventListener('click', importQuotation);

    // 承运商相关事件绑定
    document.getElementById('add-carrier-btn').addEventListener('click', showCarrierModal);
    document.getElementById('cancel-carrier-btn').addEventListener('click', hideCarrierModal);
    document.getElementById('save-carrier-btn').addEventListener('click', saveCarrier);
    document.getElementById('carrier-select').addEventListener('change', function(e) {
        selectedCarrierId = e.target.value;
        validateImportButton();
    });

    // 添加承运商代码输入框的监听事件
    document.getElementById('carrier-code-input').addEventListener('input', validateImportButton);
});

// 加载承运商列表
async function loadCarriers() {
    try {
        const response = await fetch('/api/quotations/carriers');
        const carriers = await response.json();

        const select = document.getElementById('carrier-select');
        select.innerHTML = '<option value="" data-code="">请选择承运商</option>';

        carriers.forEach(carrier => {
            const option = document.createElement('option');
            option.value = carrier.id;
            option.textContent = `${carrier.name} (${carrier.code})`;
            option.setAttribute('data-code', carrier.code);
            select.appendChild(option);
        });
    } catch (error) {
        console.error('加载承运商失败:', error);
        antd.message.error('加载承运商失败');
    }
}

// 显示新增承运商对话框
function showCarrierModal() {
    document.getElementById('carrier-modal').style.display = 'block';
    document.getElementById('carrier-name').value = '';
    document.getElementById('carrier-code').value = '';
}

// 隐藏新增承运商对话框
function hideCarrierModal() {
    document.getElementById('carrier-modal').style.display = 'none';
}

// 保存承运商
async function saveCarrier() {
    const name = document.getElementById('carrier-name').value.trim();
    const code = document.getElementById('carrier-code').value.trim();

    if (!name || !code) {
        antd.message.error('请填写完整信息');
        return;
    }

    try {
        const response = await fetch('/api/quotations/carriers', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ name, code })
        });

        const result = await response.json();
        if (!response.ok) {
            throw new Error(result.message || '保存失败');
        }

        antd.message.success('承运商添加成功');
        hideCarrierModal();
        await loadCarriers();

        // 自动选择新添加的承运商
        document.getElementById('carrier-select').value = result.id;
        selectedCarrierId = result.id;
        validateImportButton();

    } catch (error) {
        console.error('保存承运商失败:', error);
        antd.message.error(error.message || '保存失败');
    }
}

// 文件上传处理
async function handleFileUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    // 显示文件名
    document.getElementById('file-name').textContent = file.name;

    try {
        // 显示加载状态
        document.getElementById('loading').style.display = 'block';

        // 创建 FormData 对象
        const formData = new FormData();
        formData.append('file', file);

        // 发送文件上传请求
        const response = await fetch('/api/quotations/upload', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || `上传失败: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
            // 显示提取的文本内容
            document.getElementById('pdf-content').value = result.content;
            // 存储源文件路径
            document.getElementById('source-file').value = result.source_file;
            // 启用分析按钮
            document.getElementById('analyze-btn').disabled = false;
            pdfContent = result.content;
            alert('PDF文本提取成功');
        } else {
            throw new Error(result.message || '文本提取失败');
        }
    } catch (error) {
        console.error('上传处理失败:', error);
        document.getElementById('pdf-content').value = '';
        alert(error.message || '上传处理失败');
    } finally {
        // 隐藏加载状态
        document.getElementById('loading').style.display = 'none';
    }
}

// LLM分析PDF内容
async function analyzePdfContent() {
    if (!pdfContent) {
        antd.message.error('请先上传PDF文件');
        return;
    }

    try {
        showLoading(true);
        document.getElementById('analyze-btn').disabled = true;

        const response = await fetch('/api/quotations/analyze', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ content: pdfContent })
        });

        const result = await response.json();
        if (!result.success) {
            throw new Error(result.message || '解析失败');
        }

        parsedJson = result.data;
        document.getElementById('analysis-result').value = JSON.stringify(parsedJson, null, 2);
        validateImportButton();
        antd.message.success('LLM解析成功');

    } catch (error) {
        console.error('LLM解析失败:', error);
        antd.message.error(error.message || 'LLM解析失败，请稍后重试');
    } finally {
        showLoading(false);
        document.getElementById('analyze-btn').disabled = false;
    }
}

// 导入运价数据
async function importQuotation() {
    if (!parsedJson) {
        antd.message.error('请先进行LLM解析');
        return;
    }

    const carrierSelect = document.getElementById('carrier-select');
    const selectedOption = carrierSelect.options[carrierSelect.selectedIndex];
    if (!selectedOption.value) {
        antd.message.error('请选择承运商');
        return;
    }

    const carrierCode = selectedOption.getAttribute('data-code');
    const sourceFile = document.getElementById('source-file').value;

    try {
        showLoading(true);
        document.getElementById('import-btn').disabled = true;

        const requestData = {
            content: parsedJson,
            carrier_code: carrierCode,
            source_file: sourceFile
        };

        const response = await fetch('/api/quotations/import', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        const result = await response.json();
        if (!result.success) {
            throw new Error(result.message || '导入失败');
        }

        antd.message.success('运价导入成功');
        // 重置表单
        resetForm();

    } catch (error) {
        console.error('导入运价失败:', error);
        antd.message.error(error.message || '导入运价失败');
    } finally {
        showLoading(false);
        document.getElementById('import-btn').disabled = false;
    }
}

// 表单验证
function validateImportButton() {
    const carrierSelect = document.getElementById('carrier-select');
    const selectedOption = carrierSelect.options[carrierSelect.selectedIndex];
    const analysisResult = document.getElementById('analysis-result').value.trim();

    // 检查是否有解析结果和选择了承运商
    document.getElementById('import-btn').disabled = !analysisResult || !selectedOption.value;
}

// 重置表单
function resetForm() {
    document.getElementById('file-input').value = '';
    document.getElementById('file-name').textContent = '';
    document.getElementById('pdf-content').value = '';
    document.getElementById('analysis-result').value = '';
    document.getElementById('carrier-select').value = '';
    document.getElementById('source-file').value = '';
    document.getElementById('analyze-btn').disabled = true;
    document.getElementById('import-btn').disabled = true;
    parsedJson = null;
    pdfContent = '';
}

// 显示/隐藏加载状态
function showLoading(show) {
    document.getElementById('loading').style.display = show ? 'block' : 'none';
}
