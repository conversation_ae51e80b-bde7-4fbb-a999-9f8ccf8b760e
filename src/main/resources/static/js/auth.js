// 认证相关的JavaScript函数

// 检查用户是否已登录
async function checkAuth() {
    try {
        const response = await fetch('/api/auth/current');
        const result = await response.json();
        
        if (!result.success || !result.user) {
            // 未登录，重定向到首页
            alert('请先登录后再使用此功能');
            window.location.href = 'index.html';
            return false;
        }
        
        return result.user;
    } catch (error) {
        console.error('检查登录状态失败:', error);
        alert('请先登录后再使用此功能');
        window.location.href = 'index.html';
        return false;
    }
}

// 页面加载时自动检查认证状态
document.addEventListener('DOMContentLoaded', function() {
    // 如果不是首页，则检查登录状态
    if (!window.location.pathname.endsWith('index.html') && 
        !window.location.pathname.endsWith('/')) {
        checkAuth();
    }
});

// 添加登出功能到导航栏
function addLogoutToNav() {
    const navButtons = document.querySelector('.nav-buttons');
    if (navButtons && !document.getElementById('logoutNavBtn')) {
        const logoutBtn = document.createElement('a');
        logoutBtn.id = 'logoutNavBtn';
        logoutBtn.href = '#';
        logoutBtn.className = 'nav-btn';
        logoutBtn.innerHTML = '<i class="bx bx-log-out icon"></i>退出登录';
        logoutBtn.onclick = function(e) {
            e.preventDefault();
            logout();
        };
        navButtons.appendChild(logoutBtn);
    }
}

// 登出函数
async function logout() {
    try {
        const response = await fetch('/api/auth/logout', {
            method: 'POST'
        });

        const result = await response.json();
        
        if (result.success) {
            alert('已退出登录');
            window.location.href = 'index.html';
        } else {
            alert('退出登录失败');
        }
    } catch (error) {
        console.error('退出登录失败:', error);
        alert('已退出登录');
        window.location.href = 'index.html';
    }
}

// 在页面加载完成后添加登出按钮
document.addEventListener('DOMContentLoaded', function() {
    // 延迟添加登出按钮，确保页面元素已加载
    setTimeout(addLogoutToNav, 100);
});
