<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>承运商管理 - 全球运价系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .nav-bar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: rgba(255,255,255,0.1);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        .nav-btn:hover {
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
        }
        .nav-btn.primary {
            background: rgba(255,255,255,0.2);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .nav-btn .icon {
            font-size: 1.1rem;
        }
        .container {
            margin-top: 2rem;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }
        .card-header {
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .btn-primary {
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            border: none;
            border-radius: 8px;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #40a9ff, #1890ff);
            transform: translateY(-1px);
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
        }
        .modal-header {
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            color: white;
        }
        .modal-header .btn-close {
            filter: invert(1);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="nav-buttons">
            <a href="index.html" class="nav-btn">
                <i class='bx bx-home icon'></i>
                首页
            </a>
            <a href="import.html" class="nav-btn">
                <i class='bx bx-import icon'></i>
                导入运价
            </a>
            <a href="list.html" class="nav-btn">
                <i class='bx bx-list-ul icon'></i>
                运价列表
            </a>
            <a href="freight-query.html" class="nav-btn">
                <i class='bx bx-calculator icon'></i>
                运价计算
            </a>
            <a href="carriers.html" class="nav-btn primary">
                <i class='bx bx-building icon'></i>
                承运商管理
            </a>
        </div>
    </div>

    <div class="container">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class='bx bx-building'></i> 承运商管理
                    </h4>
                    <button type="button" class="btn btn-light" data-bs-toggle="modal" data-bs-target="#carrierModal" onclick="openAddModal()">
                        <i class='bx bx-plus'></i> 新增承运商
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>承运商名称</th>
                                <th>承运商代码</th>
                                <th>地址</th>
                                <th>电话</th>
                                <th>联系人</th>
                                <th>创建时间</th>
                                <th>修改时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="carriersTableBody">
                            <tr>
                                <td colspan="9" class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 承运商编辑模态框 -->
    <div class="modal fade" id="carrierModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">新增承运商</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="carrierForm">
                        <input type="hidden" id="carrierId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="carrierName" class="form-label">承运商名称 *</label>
                                <input type="text" class="form-control" id="carrierName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="carrierCode" class="form-label">承运商代码 *</label>
                                <input type="text" class="form-control" id="carrierCode" required>
                            </div>
                            <div class="col-12 mb-3">
                                <label for="carrierAddress" class="form-label">地址</label>
                                <textarea class="form-control" id="carrierAddress" rows="2"></textarea>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="carrierPhone" class="form-label">电话</label>
                                <input type="text" class="form-control" id="carrierPhone">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="carrierContacts" class="form-label">联系人</label>
                                <input type="text" class="form-control" id="carrierContacts">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveCarrier()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentCarrierId = null;

        // 页面加载时获取承运商列表
        document.addEventListener('DOMContentLoaded', function() {
            loadCarriers();
        });

        // 加载承运商列表
        function loadCarriers() {
            fetch('/api/quotations/carriers')
                .then(response => response.json())
                .then(data => {
                    renderCarriersTable(data);
                })
                .catch(error => {
                    console.error('加载承运商列表失败:', error);
                    document.getElementById('carriersTableBody').innerHTML = `
                        <tr><td colspan="9" class="text-center text-danger">加载失败，请刷新重试</td></tr>
                    `;
                });
        }

        // 渲染承运商表格
        function renderCarriersTable(carriers) {
            const tbody = document.getElementById('carriersTableBody');
            if (carriers.length === 0) {
                tbody.innerHTML = '<tr><td colspan="9" class="text-center text-muted">暂无承运商数据</td></tr>';
                return;
            }

            tbody.innerHTML = carriers.map(carrier => `
                <tr>
                    <td>${carrier.id}</td>
                    <td>${carrier.name}</td>
                    <td><span class="badge bg-secondary">${carrier.code}</span></td>
                    <td>${carrier.address || '-'}</td>
                    <td>${carrier.phone || '-'}</td>
                    <td>${carrier.contacts || '-'}</td>
                    <td>${formatDateTime(carrier.createTime)}</td>
                    <td>${formatDateTime(carrier.updateTime)}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary me-1" onclick="editCarrier(${carrier.id})">
                            <i class='bx bx-edit'></i> 编辑
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteCarrier(${carrier.id}, '${carrier.name}')">
                            <i class='bx bx-trash'></i> 删除
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '-';
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN');
        }

        // 打开新增模态框
        function openAddModal() {
            currentCarrierId = null;
            document.getElementById('modalTitle').textContent = '新增承运商';
            document.getElementById('carrierForm').reset();
            document.getElementById('carrierId').value = '';
        }

        // 编辑承运商
        function editCarrier(id) {
            fetch(`/api/quotations/carriers/${id}`)
                .then(response => response.json())
                .then(carrier => {
                    currentCarrierId = id;
                    document.getElementById('modalTitle').textContent = '编辑承运商';
                    document.getElementById('carrierId').value = carrier.id;
                    document.getElementById('carrierName').value = carrier.name;
                    document.getElementById('carrierCode').value = carrier.code;
                    document.getElementById('carrierAddress').value = carrier.address || '';
                    document.getElementById('carrierPhone').value = carrier.phone || '';
                    document.getElementById('carrierContacts').value = carrier.contacts || '';
                    
                    new bootstrap.Modal(document.getElementById('carrierModal')).show();
                })
                .catch(error => {
                    console.error('获取承运商信息失败:', error);
                    alert('获取承运商信息失败');
                });
        }

        // 保存承运商
        function saveCarrier() {
            const form = document.getElementById('carrierForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const carrierData = {
                name: document.getElementById('carrierName').value,
                code: document.getElementById('carrierCode').value,
                address: document.getElementById('carrierAddress').value,
                phone: document.getElementById('carrierPhone').value,
                contacts: document.getElementById('carrierContacts').value
            };

            const url = currentCarrierId ? 
                `/api/quotations/carriers/${currentCarrierId}` : 
                '/api/quotations/carriers';
            const method = currentCarrierId ? 'PUT' : 'POST';

            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(carrierData)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    alert(result.message);
                    bootstrap.Modal.getInstance(document.getElementById('carrierModal')).hide();
                    loadCarriers();
                } else {
                    alert(result.message || '保存失败');
                }
            })
            .catch(error => {
                console.error('保存承运商失败:', error);
                alert('保存失败，请稍后重试');
            });
        }

        // 删除承运商
        function deleteCarrier(id, name) {
            if (!confirm(`确定要删除承运商 "${name}" 吗？`)) {
                return;
            }

            fetch(`/api/quotations/carriers/${id}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    alert(result.message);
                    loadCarriers();
                } else {
                    alert(result.message || '删除失败');
                }
            })
            .catch(error => {
                console.error('删除承运商失败:', error);
                alert('删除失败，请稍后重试');
            });
        }
    </script>
</body>
</html>
