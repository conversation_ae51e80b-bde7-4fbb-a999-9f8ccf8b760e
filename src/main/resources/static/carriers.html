<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>承运商管理 - 全球运价系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
    <script src="js/auth.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .nav-bar {
            width: 100%;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 10;
            background: rgba(255,255,255,0.92);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-center {
            display: flex;
            justify-content: center;
            flex: 1;
        }
        .nav-buttons {
            display: flex;
            gap: 12px;
        }
        .nav-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s;
            background: #fff;
        }
        .nav-btn:hover {
            background: #f5f7fa;
            color: #1890ff;
        }
        .nav-btn.primary {
            background: #1890ff;
            color: #fff;
            border-color: #1890ff;
        }
        .nav-btn.primary:hover {
            background: #40a9ff;
            color: #fff;
        }

        .logout-btn {
            background: #ff4d4f;
            color: #fff;
            border-color: #ff4d4f;
        }

        .logout-btn:hover {
            background: #ff7875;
            color: #fff;
        }
        .icon {
            font-size: 18px;
        }


        .container {
            margin-top: 80px !important;
            max-width: 100% !important;
            padding: 0 30px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }
        .card-header {
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .btn-primary {
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            border: none;
            border-radius: 8px;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #40a9ff, #1890ff);
            transform: translateY(-1px);
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
        }
        .modal-header {
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            color: white;
        }
        .modal-header .btn-close {
            filter: invert(1);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <div class="nav-bar">
        <div></div> <!-- 左侧占位 -->
        <div class="nav-center">
            <div class="nav-buttons">
                <a href="index.html" class="nav-btn">
                    <i class='bx bx-home icon'></i>
                    首页
                </a>
                <a href="import.html" class="nav-btn">
                    <i class='bx bx-import icon'></i>
                    导入运价
                </a>
                <a href="list.html" class="nav-btn">
                    <i class='bx bx-list-ul icon'></i>
                    运价列表
                </a>
                <a href="freight-query.html" class="nav-btn">
                    <i class='bx bx-calculator icon'></i>
                    运价计算
                </a>
                <a href="carriers.html" class="nav-btn primary">
                    <i class='bx bx-building icon'></i>
                    承运商管理
                </a>
                <a href="users.html" class="nav-btn" id="usersBtn">
                    <i class='bx bx-user icon'></i>
                    用户管理
                </a>
            </div>
        </div>
        <div class="nav-buttons">
            <button class="nav-btn logout-btn" onclick="logout()">
                <i class='bx bx-log-out icon'></i>
                退出登录
            </button>
        </div>
    </div>

    <!-- 查询条件卡片 -->
    <div class="container mt-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class='bx bx-search'></i> 查询条件</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">承运商名称</label>
                        <input type="text" class="form-control" id="searchName" placeholder="请输入承运商名称" onkeypress="handleEnterKey(event)">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">承运商代码</label>
                        <input type="text" class="form-control" id="searchCode" placeholder="请输入承运商代码" onkeypress="handleEnterKey(event)">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">联系人</label>
                        <input type="text" class="form-control" id="searchContact" placeholder="请输入联系人" onkeypress="handleEnterKey(event)">
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button class="btn btn-primary me-2" onclick="searchCarriers()">
                            <i class='bx bx-search'></i> 查询
                        </button>
                        <button class="btn btn-outline-secondary" onclick="resetSearch()">
                            <i class='bx bx-refresh'></i> 重置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 承运商列表卡片 -->
    <div class="container">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class='bx bx-building'></i> 承运商管理
                    </h4>
                    <button type="button" class="btn btn-light" data-bs-toggle="modal" data-bs-target="#carrierModal" onclick="openAddModal()">
                        <i class='bx bx-plus'></i> 新增承运商
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>承运商名称</th>
                                <th>承运商代码</th>
                                <th>地址</th>
                                <th>电话</th>
                                <th>联系人</th>
                                <th>创建时间</th>
                                <th>修改时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="carriersTableBody">
                            <tr>
                                <td colspan="9" class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 承运商编辑模态框 -->
    <div class="modal fade" id="carrierModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">新增承运商</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="carrierForm">
                        <input type="hidden" id="carrierId">
                        <div class="row">
                            <div class="col-md-6 mb-3">

                                <label for="carrierName" class="form-label">承运商名称 *</label>
                                <input type="text" class="form-control" id="carrierName" required>

                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="carrierCode" class="form-label">承运商代码 *</label>
                                <input type="text" class="form-control" id="carrierCode" required>
                            </div>
                            <div class="col-12 mb-3">
                                <label for="carrierAddress" class="form-label">地址</label>
                                <textarea class="form-control" id="carrierAddress" rows="2"></textarea>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="carrierPhone" class="form-label">电话</label>
                                <input type="text" class="form-control" id="carrierPhone">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="carrierContacts" class="form-label">联系人</label>
                                <input type="text" class="form-control" id="carrierContacts">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveCarrier()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentCarrierId = null;

        // 根据用户角色更新导航栏
        function updateNavigationByRole(user) {
            if (user.realname !== 'Admin') {
                // 隐藏管理员专用按钮
                const usersBtn = document.getElementById('usersBtn');
                if (usersBtn) usersBtn.style.display = 'none';
            }
        }

        // 页面加载时检查权限并获取承运商列表
        document.addEventListener('DOMContentLoaded', async function() {
            // 检查登录状态和权限
            const user = await checkAuth();
            if (!user) {
                return; // checkAuth函数会处理重定向
            }

            // 检查是否为管理员
            if (user.realname !== 'Admin') {
                alert('您没有权限访问此页面，仅管理员可用');
                window.location.href = 'index.html';
                return;
            }

            // 根据权限显示/隐藏按钮
            updateNavigationByRole(user);

            loadCarriers();
        });

        // 加载承运商列表
        function loadCarriers() {
            fetch('/api/quotations/carriers')
                .then(response => response.json())
                .then(data => {
                    displayCarriers(data);
                })
                .catch(error => {
                    console.error('加载承运商列表失败:', error);
                    document.getElementById('carriersTableBody').innerHTML = `
                        <tr><td colspan="9" class="text-center text-danger">加载失败，请刷新重试</td></tr>
                    `;
                });
        }



        // 打开新增模态框
        function openAddModal() {
            currentCarrierId = null;
            document.getElementById('modalTitle').textContent = '新增承运商';
            document.getElementById('carrierForm').reset();
            document.getElementById('carrierId').value = '';

            // 新增时，承运商名称和代码可编辑
            document.getElementById('carrierName').readOnly = false;
            document.getElementById('carrierCode').readOnly = false;
            document.getElementById('carrierName').style.backgroundColor = '';
            document.getElementById('carrierCode').style.backgroundColor = '';
        }

        // 编辑承运商
        function editCarrier(id) {
            fetch(`/api/quotations/carriers/${id}`)
                .then(response => response.json())
                .then(carrier => {
                    currentCarrierId = id;
                    document.getElementById('modalTitle').textContent = '编辑承运商';
                    document.getElementById('carrierId').value = carrier.id;
                    document.getElementById('carrierName').value = carrier.name;
                    document.getElementById('carrierCode').value = carrier.code;
                    document.getElementById('carrierAddress').value = carrier.address || '';
                    document.getElementById('carrierPhone').value = carrier.phone || '';
                    document.getElementById('carrierContacts').value = carrier.contacts || '';

                    // 编辑时，承运商名称和代码不可编辑
                    document.getElementById('carrierName').readOnly = true;
                    document.getElementById('carrierCode').readOnly = true;
                    document.getElementById('carrierName').style.backgroundColor = '#f8f9fa';
                    document.getElementById('carrierCode').style.backgroundColor = '#f8f9fa';

                    new bootstrap.Modal(document.getElementById('carrierModal')).show();
                })
                .catch(error => {
                    console.error('获取承运商信息失败:', error);
                    alert('获取承运商信息失败');
                });
        }

        // 保存承运商
        function saveCarrier() {
            const form = document.getElementById('carrierForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // 如果是编辑模式，显示确认提示
            if (currentCarrierId) {
                if (!confirm('确定要修改此承运商信息吗？')) {
                    return; // 用户取消修改
                }
            }

            const carrierData = {
                name: document.getElementById('carrierName').value,
                code: document.getElementById('carrierCode').value,
                address: document.getElementById('carrierAddress').value,
                phone: document.getElementById('carrierPhone').value,
                contacts: document.getElementById('carrierContacts').value
            };

            const url = currentCarrierId ?
                `/api/quotations/carriers/${currentCarrierId}` :
                '/api/quotations/carriers';
            const method = currentCarrierId ? 'PUT' : 'POST';

            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(carrierData)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    alert(result.message);
                    bootstrap.Modal.getInstance(document.getElementById('carrierModal')).hide();
                    loadCarriers();
                } else {
                    alert(result.message || '保存失败');
                }
            })
            .catch(error => {
                console.error('保存承运商失败:', error);
                alert('保存失败，请稍后重试');
            });
        }

        // 删除承运商
        function deleteCarrier(id, name) {
            if (!confirm(`确定要删除承运商 "${name}" 吗？`)) {
                return;
            }

            fetch(`/api/quotations/carriers/${id}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    alert(result.message);
                    loadCarriers();
                } else {
                    alert(result.message || '删除失败');
                }
            })
            .catch(error => {
                console.error('删除承运商失败:', error);
                alert('删除失败，请稍后重试');
            });
        }

        // 查询承运商
        function searchCarriers() {
            const searchName = document.getElementById('searchName').value.trim();
            const searchCode = document.getElementById('searchCode').value.trim();
            const searchContact = document.getElementById('searchContact').value.trim();

            fetch('/api/quotations/carriers')
            .then(response => response.json())
            .then(carriers => {
                // 过滤承运商数据
                let filteredCarriers = carriers.filter(carrier => {
                    let nameMatch = !searchName || (carrier.name && carrier.name.toLowerCase().includes(searchName.toLowerCase()));
                    let codeMatch = !searchCode || (carrier.code && carrier.code.toLowerCase().includes(searchCode.toLowerCase()));
                    let contactMatch = !searchContact || (carrier.contacts && carrier.contacts.toLowerCase().includes(searchContact.toLowerCase()));

                    return nameMatch && codeMatch && contactMatch;
                });

                displayCarriers(filteredCarriers);
            })
            .catch(error => {
                console.error('查询承运商失败:', error);
                alert('查询失败，请稍后重试');
            });
        }

        // 重置查询条件
        function resetSearch() {
            document.getElementById('searchName').value = '';
            document.getElementById('searchCode').value = '';
            document.getElementById('searchContact').value = '';
            loadCarriers(); // 重新加载所有数据
        }

        // 处理回车键查询
        function handleEnterKey(event) {
            if (event.key === 'Enter') {
                searchCarriers();
            }
        }

        // 显示承运商数据的通用函数
        function displayCarriers(carriers) {
            const tbody = document.getElementById('carriersTableBody');

            if (carriers.length === 0) {
                tbody.innerHTML = '<tr><td colspan="9" class="text-center text-muted">暂无数据</td></tr>';
                return;
            }

            tbody.innerHTML = carriers.map(carrier => `
                <tr>
                    <td>${carrier.id}</td>
                    <td>${carrier.name || '-'}</td>
                    <td><span class="badge bg-secondary">${carrier.code || '-'}</span></td>
                    <td>${carrier.address || '-'}</td>
                    <td>${carrier.phone || '-'}</td>
                    <td>${carrier.contacts || '-'}</td>
                    <td>${carrier.createTime ? new Date(carrier.createTime).toLocaleString() : '-'}</td>
                    <td>${carrier.updateTime ? new Date(carrier.updateTime).toLocaleString() : '-'}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary me-1" onclick="editCarrier(${carrier.id})">
                            <i class='bx bx-edit'></i> 编辑
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteCarrier(${carrier.id}, '${carrier.name}')">
                            <i class='bx bx-trash'></i> 删除
                        </button>
                    </td>
                </tr>
            `).join('');
        }
    </script>
</body>
</html>
