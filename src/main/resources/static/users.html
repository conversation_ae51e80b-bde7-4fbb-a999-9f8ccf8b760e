<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 全球运价查询系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .nav-bar {
            width: 100%;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 10;
            background: rgba(255,255,255,0.92);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .nav-center {
            display: flex;
            justify-content: center;
            flex: 1;
        }
        .nav-buttons {
            display: flex;
            gap: 8px;
        }
        .nav-btn {
            padding: 10px 20px;
            background: rgba(255,255,255,0.9);
            border: 1px solid rgba(102,126,234,0.3);
            border-radius: 25px;
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .nav-btn:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102,126,234,0.3);
        }
        .nav-btn.primary {
            background: #667eea;
            color: white;
        }
        .nav-btn.primary:hover {
            background: #5a6fd8;
        }
        .logout-btn {
            background: #dc3545 !important;
            color: white !important;
            border: none;
        }
        .logout-btn:hover {
            background: #c82333 !important;
        }
        .icon {
            font-size: 18px;
        }
        .main-content {
            margin-top: 80px;
            padding: 40px 20px;
            min-height: calc(100vh - 80px);
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .page-header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }
        .page-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        .page-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        .content-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        .btn-primary {
            background: #667eea;
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 500;
        }
        .btn-primary:hover {
            background: #5a6fd8;
        }
        .btn-success {
            background: #28a745;
            border: none;
            border-radius: 25px;
            padding: 8px 20px;
            font-weight: 500;
        }
        .btn-danger {
            background: #dc3545;
            border: none;
            border-radius: 25px;
            padding: 8px 20px;
            font-weight: 500;
        }
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        .table th {
            background: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #495057;
        }
        .table td {
            border: none;
            vertical-align: middle;
        }
        .modal-content {
            border-radius: 15px;
            border: none;
        }
        .modal-header {
            background: #667eea;
            color: white;
            border-radius: 15px 15px 0 0;
        }
        .form-control {
            border-radius: 10px;
            border: 1px solid #ddd;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102,126,234,0.25);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <div class="nav-bar">
        <div></div> <!-- 左侧占位 -->
        <div class="nav-center">
            <div class="nav-buttons">
                <a href="index.html" class="nav-btn">
                    <i class='bx bx-home icon'></i>
                    首页
                </a>
                <a href="import.html" class="nav-btn">
                    <i class='bx bx-import icon'></i>
                    导入运价
                </a>
                <a href="list.html" class="nav-btn">
                    <i class='bx bx-list-ul icon'></i>
                    运价列表
                </a>
                <a href="freight-query.html" class="nav-btn">
                    <i class='bx bx-calculator icon'></i>
                    运价计算
                </a>
                <a href="carriers.html" class="nav-btn" id="carriersBtn">
                    <i class='bx bx-building icon'></i>
                    承运商管理
                </a>
                <a href="users.html" class="nav-btn primary" id="usersBtn">
                    <i class='bx bx-user icon'></i>
                    用户管理
                </a>
            </div>
        </div>
        <div class="nav-buttons">
            <button class="nav-btn logout-btn" onclick="logout()">
                <i class='bx bx-log-out icon'></i>
                退出登录
            </button>
        </div>
    </div>

    <div class="main-content">
        <div class="container">
            <div class="page-header">
                <h1><i class='bx bx-user'></i> 用户管理</h1>
                <p>管理系统用户账户</p>
            </div>

            <div class="content-card">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h3>用户列表</h3>
                    <button class="btn btn-primary" onclick="showAddUserModal()">
                        <i class='bx bx-plus'></i> 新增用户
                    </button>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户名</th>
                                <th>姓名</th>
                                <th>电话</th>
                                <th>角色</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <!-- 用户数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增用户模态框 -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">新增用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm">
                        <div class="mb-3">
                            <label class="form-label">用户名 *</label>
                            <input type="text" class="form-control" id="addUsername" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">密码 *</label>
                            <input type="password" class="form-control" id="addPassword" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">姓名</label>
                            <input type="text" class="form-control" id="addName">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">电话</label>
                            <input type="text" class="form-control" id="addPhone">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">角色</label>
                            <select class="form-control" id="addRealname">
                                <option value="query">普通用户</option>
                                <option value="Admin">管理员</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="addUser()">创建用户</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑用户模态框 -->
    <div class="modal fade" id="editUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editUserForm">
                        <input type="hidden" id="editUserId">
                        <div class="mb-3">
                            <label class="form-label">用户名</label>
                            <input type="text" class="form-control" id="editUsername" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">姓名</label>
                            <input type="text" class="form-control" id="editName">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">电话</label>
                            <input type="text" class="form-control" id="editPhone">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">角色</label>
                            <select class="form-control" id="editRealname">
                                <option value="query">普通用户</option>
                                <option value="Admin">管理员</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="updateUser()">保存修改</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/auth.js"></script>
    <script>
        let currentUser = null;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
            loadUsers();
        });

        // 检查认证状态
        async function checkAuth() {
            try {
                const response = await fetch('/api/auth/check');
                const result = await response.json();

                if (!result.success || !result.user) {
                    alert('请先登录');
                    window.location.href = 'index.html';
                    return;
                }

                currentUser = result.user;

                // 检查管理员权限
                if (currentUser.realname !== 'Admin') {
                    alert('权限不足，仅管理员可访问');
                    window.location.href = 'index.html';
                    return;
                }

                // 隐藏非管理员功能
                updateUIForAdmin();

            } catch (error) {
                console.error('认证检查失败:', error);
                alert('认证检查失败，请重新登录');
                window.location.href = 'index.html';
            }
        }

        // 更新管理员UI
        function updateUIForAdmin() {
            const carriersBtn = document.getElementById('carriersBtn');
            const usersBtn = document.getElementById('usersBtn');

            if (carriersBtn) carriersBtn.style.display = 'flex';
            if (usersBtn) usersBtn.style.display = 'flex';
        }

        // 加载用户列表
        async function loadUsers() {
            try {
                const response = await fetch('/api/users');
                const result = await response.json();

                if (result.success) {
                    displayUsers(result.data);
                } else {
                    alert('加载用户列表失败: ' + result.message);
                }
            } catch (error) {
                console.error('加载用户列表失败:', error);
                alert('加载用户列表失败');
            }
        }

        // 显示用户列表
        function displayUsers(users) {
            const tbody = document.getElementById('usersTableBody');
            tbody.innerHTML = '';

            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${user.id}</td>
                    <td>${user.username}</td>
                    <td>${user.name || '-'}</td>
                    <td>${user.phone || '-'}</td>
                    <td>
                        <span class="badge ${user.realname === 'Admin' ? 'bg-danger' : 'bg-primary'}">
                            ${user.realname === 'Admin' ? '管理员' : '普通用户'}
                        </span>
                    </td>
                    <td>${formatDateTime(user.createTime)}</td>
                    <td>
                        <button class="btn btn-sm btn-success me-2" onclick="editUser(${user.id})">
                            <i class='bx bx-edit'></i> 编辑
                        </button>
                        ${user.id !== currentUser.id ? `
                            <button class="btn btn-sm btn-danger" onclick="deleteUser(${user.id})">
                                <i class='bx bx-trash'></i> 删除
                            </button>
                        ` : ''}
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '-';
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN');
        }

        // 显示新增用户模态框
        function showAddUserModal() {
            document.getElementById('addUserForm').reset();
            new bootstrap.Modal(document.getElementById('addUserModal')).show();
        }

        // 新增用户
        async function addUser() {
            const username = document.getElementById('addUsername').value.trim();
            const password = document.getElementById('addPassword').value.trim();
            const name = document.getElementById('addName').value.trim();
            const phone = document.getElementById('addPhone').value.trim();
            const realname = document.getElementById('addRealname').value;

            if (!username) {
                alert('请输入用户名');
                return;
            }

            if (!password) {
                alert('请输入密码');
                return;
            }

            try {
                const response = await fetch('/api/users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        name: name,
                        phone: phone,
                        realname: realname
                    })
                });

                const result = await response.json();

                if (result.success) {
                    alert('用户创建成功');
                    bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();
                    loadUsers(); // 重新加载用户列表
                } else {
                    alert('创建用户失败: ' + result.message);
                }
            } catch (error) {
                console.error('创建用户失败:', error);
                alert('创建用户失败');
            }
        }

        // 编辑用户
        async function editUser(userId) {
            try {
                const response = await fetch(`/api/users/${userId}`);
                const result = await response.json();

                if (result.success) {
                    const user = result.data;
                    document.getElementById('editUserId').value = user.id;
                    document.getElementById('editUsername').value = user.username;
                    document.getElementById('editName').value = user.name || '';
                    document.getElementById('editPhone').value = user.phone || '';
                    document.getElementById('editRealname').value = user.realname || 'query';

                    new bootstrap.Modal(document.getElementById('editUserModal')).show();
                } else {
                    alert('获取用户信息失败: ' + result.message);
                }
            } catch (error) {
                console.error('获取用户信息失败:', error);
                alert('获取用户信息失败');
            }
        }

        // 更新用户
        async function updateUser() {
            const userId = document.getElementById('editUserId').value;
            const name = document.getElementById('editName').value.trim();
            const phone = document.getElementById('editPhone').value.trim();
            const realname = document.getElementById('editRealname').value;

            if (!confirm('确定要保存修改吗？')) {
                return;
            }

            try {
                const response = await fetch(`/api/users/${userId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: name,
                        phone: phone,
                        realname: realname
                    })
                });

                const result = await response.json();

                if (result.success) {
                    alert('用户信息更新成功');
                    bootstrap.Modal.getInstance(document.getElementById('editUserModal')).hide();
                    loadUsers(); // 重新加载用户列表
                } else {
                    alert('更新用户信息失败: ' + result.message);
                }
            } catch (error) {
                console.error('更新用户信息失败:', error);
                alert('更新用户信息失败');
            }
        }

        // 删除用户
        async function deleteUser(userId) {
            if (!confirm('确定要删除这个用户吗？此操作不可恢复！')) {
                return;
            }

            try {
                const response = await fetch(`/api/users/${userId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    alert('用户删除成功');
                    loadUsers(); // 重新加载用户列表
                } else {
                    alert('删除用户失败: ' + result.message);
                }
            } catch (error) {
                console.error('删除用户失败:', error);
                alert('删除用户失败');
            }
        }

        // 退出登录
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                fetch('/api/auth/logout', { method: 'POST' })
                    .then(() => {
                        window.location.href = 'index.html';
                    })
                    .catch(error => {
                        console.error('退出登录失败:', error);
                        window.location.href = 'index.html';
                    });
            }
        }
    </script>
</body>
</html>
