<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 全球运价系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
    <script src="js/auth.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .nav-bar {
            width: 100%;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 10;
            background: rgba(255,255,255,0.92);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-center {
            display: flex;
            justify-content: center;
            flex: 1;
        }
        .nav-buttons {
            display: flex;
            gap: 12px;
        }
        .nav-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s;
            background: #fff;
        }
        .nav-btn:hover {
            background: #f5f7fa;
            color: #1890ff;
        }
        .nav-btn.primary {
            background: #1890ff;
            color: #fff;
            border-color: #1890ff;
        }
        .nav-btn.primary:hover {
            background: #40a9ff;
            color: #fff;
        }
        .logout-btn {
            background: #ff4d4f;
            color: #fff;
            border-color: #ff4d4f;
        }
        .logout-btn:hover {
            background: #ff7875;
            color: #fff;
        }
        .content {
            margin-top: 80px;
            padding: 20px;
        }
        .card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 24px;
            margin-bottom: 20px;
        }
        .card-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
        }
        .table {
            margin-bottom: 0;
        }
        .table th {
            background-color: #fafafa;
            border-bottom: 2px solid #e8e8e8;
            font-weight: 600;
            color: #333;
        }
        .table td {
            vertical-align: middle;
        }
        .badge {
            font-size: 12px;
            padding: 4px 8px;
        }
        .badge.admin {
            background-color: #52c41a;
        }
        .badge.query {
            background-color: #1890ff;
        }
        .btn-group {
            display: flex;
            gap: 8px;
        }
        .btn-sm {
            padding: 4px 12px;
            font-size: 12px;
        }
        .btn-edit {
            background-color: #1890ff;
            border-color: #1890ff;
            color: #fff;
        }
        .btn-edit:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
            color: #fff;
        }
        .btn-delete {
            background-color: #ff4d4f;
            border-color: #ff4d4f;
            color: #fff;
        }
        .btn-delete:hover {
            background-color: #ff7875;
            border-color: #ff7875;
            color: #fff;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .empty {
            text-align: center;
            padding: 40px;
            color: #999;
        }
    </style>
</head>
<body>
    <div class="nav-bar">
        <div></div> <!-- 左侧占位 -->
        <div class="nav-center">
            <div class="nav-buttons">
                <a href="index.html" class="nav-btn">
                    <i class='bx bx-home icon'></i>
                    首页
                </a>
                <a href="import.html" class="nav-btn">
                    <i class='bx bx-import icon'></i>
                    导入运价
                </a>
                <a href="list.html" class="nav-btn">
                    <i class='bx bx-list-ul icon'></i>
                    运价列表
                </a>
                <a href="freight-query.html" class="nav-btn">
                    <i class='bx bx-calculator icon'></i>
                    运价计算
                </a>
                <a href="carriers.html" class="nav-btn" id="carriersBtn">
                    <i class='bx bx-building icon'></i>
                    承运商管理
                </a>
                <a href="users.html" class="nav-btn primary" id="usersBtn">
                    <i class='bx bx-user icon'></i>
                    用户管理
                </a>
            </div>
        </div>
        <div class="nav-buttons">
            <button class="nav-btn logout-btn" onclick="logout()">
                <i class='bx bx-log-out icon'></i>
                退出登录
            </button>
        </div>
    </div>

    <div class="content">
        <div class="card">
            <div class="card-title">
                <i class='bx bx-user'></i>
                用户管理
            </div>
            
            <div id="loading" class="loading" style="display: none;">
                <i class='bx bx-loader-alt bx-spin'></i>
                加载中...
            </div>
            
            <div id="userTable" style="display: none;">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户名</th>
                                <th>姓名</th>
                                <th>电话</th>
                                <th>角色</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="userTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div id="emptyState" class="empty" style="display: none;">
                <i class='bx bx-user-x' style="font-size: 48px; margin-bottom: 16px;"></i>
                <div>暂无用户数据</div>
            </div>
        </div>
    </div>

    <!-- 编辑用户模态框 -->
    <div class="modal fade" id="editUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editUserForm">
                        <input type="hidden" id="editUserId">
                        <div class="mb-3">
                            <label for="editUserName" class="form-label">姓名</label>
                            <input type="text" class="form-control" id="editUserName" required>
                        </div>
                        <div class="mb-3">
                            <label for="editUserPhone" class="form-label">电话</label>
                            <input type="text" class="form-control" id="editUserPhone">
                        </div>
                        <div class="mb-3">
                            <label for="editUserRealname" class="form-label">角色</label>
                            <select class="form-control" id="editUserRealname" required>
                                <option value="query">普通用户</option>
                                <option value="Admin">管理员</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveUser()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentEditUserId = null;

        // 页面加载时检查权限并获取用户列表
        document.addEventListener('DOMContentLoaded', async function() {
            // 检查登录状态和权限
            const user = await checkAuth();
            if (!user) {
                return; // checkAuth函数会处理重定向
            }
            
            // 检查是否为管理员
            if (user.realname !== 'Admin') {
                alert('您没有权限访问此页面，仅管理员可用');
                window.location.href = 'index.html';
                return;
            }
            
            // 根据权限显示/隐藏按钮
            updateNavigationByRole(user);
            
            loadUsers();
        });

        // 根据用户角色更新导航栏
        function updateNavigationByRole(user) {
            if (user.realname !== 'Admin') {
                // 隐藏管理员专用按钮
                const carriersBtn = document.getElementById('carriersBtn');
                const usersBtn = document.getElementById('usersBtn');
                if (carriersBtn) carriersBtn.style.display = 'none';
                if (usersBtn) usersBtn.style.display = 'none';
            }
        }

        // 加载用户列表
        async function loadUsers() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('userTable').style.display = 'none';
            document.getElementById('emptyState').style.display = 'none';
            
            try {
                const response = await fetch('/api/users');
                const result = await response.json();
                
                if (result.success) {
                    displayUsers(result.data);
                } else {
                    alert(result.message || '获取用户列表失败');
                    if (result.message && result.message.includes('权限不足')) {
                        window.location.href = 'index.html';
                    }
                }
            } catch (error) {
                console.error('获取用户列表失败:', error);
                alert('获取用户列表失败，请稍后重试');
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        }

        // 显示用户列表
        function displayUsers(users) {
            const tbody = document.getElementById('userTableBody');
            
            if (!users || users.length === 0) {
                document.getElementById('emptyState').style.display = 'block';
                return;
            }
            
            tbody.innerHTML = '';
            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${user.id}</td>
                    <td>${user.username}</td>
                    <td>${user.name || '-'}</td>
                    <td>${user.phone || '-'}</td>
                    <td>
                        <span class="badge ${user.realname === 'Admin' ? 'admin' : 'query'}">
                            ${user.realname === 'Admin' ? '管理员' : '普通用户'}
                        </span>
                    </td>
                    <td>${formatDateTime(user.createTime)}</td>
                    <td>
                        <div class="btn-group">
                            <button class="btn btn-edit btn-sm" onclick="editUser(${user.id})">
                                <i class='bx bx-edit'></i> 编辑
                            </button>
                            <button class="btn btn-delete btn-sm" onclick="deleteUser(${user.id}, '${user.username}')">
                                <i class='bx bx-trash'></i> 删除
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
            
            document.getElementById('userTable').style.display = 'block';
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '-';
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN');
        }

        // 编辑用户
        async function editUser(userId) {
            try {
                const response = await fetch(`/api/users/${userId}`);
                const result = await response.json();
                
                if (result.success) {
                    const user = result.data;
                    currentEditUserId = userId;
                    
                    document.getElementById('editUserId').value = user.id;
                    document.getElementById('editUserName').value = user.name || '';
                    document.getElementById('editUserPhone').value = user.phone || '';
                    document.getElementById('editUserRealname').value = user.realname || 'query';
                    
                    const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
                    modal.show();
                } else {
                    alert(result.message || '获取用户信息失败');
                }
            } catch (error) {
                console.error('获取用户信息失败:', error);
                alert('获取用户信息失败，请稍后重试');
            }
        }

        // 保存用户
        async function saveUser() {
            const userId = currentEditUserId;
            const name = document.getElementById('editUserName').value.trim();
            const phone = document.getElementById('editUserPhone').value.trim();
            const realname = document.getElementById('editUserRealname').value;
            
            if (!name) {
                alert('请输入姓名');
                return;
            }
            
            try {
                const response = await fetch(`/api/users/${userId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: name,
                        phone: phone,
                        realname: realname
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('用户信息更新成功');
                    bootstrap.Modal.getInstance(document.getElementById('editUserModal')).hide();
                    loadUsers(); // 重新加载用户列表
                } else {
                    alert(result.message || '更新用户信息失败');
                }
            } catch (error) {
                console.error('更新用户信息失败:', error);
                alert('更新用户信息失败，请稍后重试');
            }
        }

        // 删除用户
        async function deleteUser(userId, username) {
            if (!confirm(`确定要删除用户 "${username}" 吗？此操作不可恢复。`)) {
                return;
            }
            
            try {
                const response = await fetch(`/api/users/${userId}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('用户删除成功');
                    loadUsers(); // 重新加载用户列表
                } else {
                    alert(result.message || '删除用户失败');
                }
            } catch (error) {
                console.error('删除用户失败:', error);
                alert('删除用户失败，请稍后重试');
            }
        }
    </script>
</body>
</html>
