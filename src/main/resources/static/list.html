<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>报价列表</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">
    <script src="js/auth.js"></script>
    <style>
        :root {
            --primary-color: #1890ff;
            --border-color: #e8e8e8;
            --bg-color: #f5f7fa;
            --text-color: #333;
            --text-secondary: #666;
        }
        
        body { 
            margin: 0; 
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-color);
            color: var(--text-color);
        }
        
        .layout {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .nav-bar {
            width: 100%;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 10;
            background: rgba(255,255,255,0.92);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            padding: 16px 0 16px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-left: 20px;
            padding-right: 20px;
        }
        
        .nav-buttons {
            display: flex;
            gap: 12px;
        }
        
        .nav-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s;
            background: #fff;
        }
        
        .nav-btn:hover {
            background: #f5f7fa;
            color: #1890ff;
        }
        
        .nav-btn.primary {
            background: #1890ff;
            color: #fff;
            border-color: #1890ff;
        }
        
        .nav-btn.primary:hover {
            background: #40a9ff;
            color: #fff;
        }
        
        .content {
            flex: 1;
            padding: 0 24px 24px;
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 24px;
            color: var(--text-color);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            padding: 24px;
            margin-bottom: 24px;
            border: 1px solid var(--border-color);
        }
        
        .search-form {
            display: flex;
            gap: 24px;
            align-items: flex-end;
        }
        
        .form-group {
            flex: 1;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-color);
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: #fff;
            color: var(--text-color);
            transition: all 0.3s;
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: #fff;
            color: var(--text-color);
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
        }
        
        .btn:hover {
            background: var(--bg-color);
            color: var(--primary-color);
        }
        
        .btn.primary {
            background: var(--primary-color);
            color: #fff;
            border-color: var(--primary-color);
        }
        
        .btn.primary:hover {
            background: #40a9ff;
        }
        
        .btn.secondary {
            background: #f5f5f5;
            color: var(--text-secondary);
        }
        
        .btn.secondary:hover {
            background: #e8e8e8;
            color: var(--text-color);
        }
        
        .table-card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }
        
        .table {
            margin: 0;
        }
        
        .table th {
            background: var(--bg-color);
            font-weight: 500;
            color: var(--text-color);
            border-bottom: 1px solid var(--border-color);
            padding: 12px 16px;
        }
        
        .table td {
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-color);
            vertical-align: middle;
        }
        
        .table tbody tr:hover {
            background: var(--bg-color);
        }
        
        .table tbody tr:last-child td {
            border-bottom: none;
        }
        
        .detail-link {
            color: var(--primary-color);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }
        
        .detail-link:hover {
            color: #40a9ff;
        }
        
        .status-tag {
            display: inline-flex;
            align-items: center;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .status-tag.active {
            background: #e6f7ff;
            color: var(--primary-color);
            border: 1px solid #91d5ff;
        }
        
        .status-tag.inactive {
            background: #f5f5f5;
            color: var(--text-secondary);
            border: 1px solid #d9d9d9;
        }
        
        .icon {
            font-size: 18px;
        }
        
        .layout, .content {
            margin-top: 80px !important;
            max-width: 100% !important;
            padding: 0 30px;
        }
    </style>
</head>
<body>
    <div class="nav-bar">
        <div class="nav-buttons">
            <a href="index.html" class="nav-btn">
                <i class='bx bx-home icon'></i>
                首页
            </a>
            <a href="import.html" class="nav-btn">
                <i class='bx bx-import icon'></i>
                导入运价
            </a>
            <a href="list.html" class="nav-btn primary">
                <i class='bx bx-list-ul icon'></i>
                运价列表
            </a>
            <a href="freight-query.html" class="nav-btn">
                <i class='bx bx-calculator icon'></i>
                运价计算
            </a>
            <a href="carriers.html" class="nav-btn">
                <i class='bx bx-building icon'></i>
                承运商管理
            </a>
        </div>
    </div>
    <div class="layout">
        <div class="content">
            <div class="page-title">
                <i class='bx bx-list-ul icon'></i>
                报价列表
            </div>

            <div class="search-card">
                <form id="searchForm" class="search-form">
                    <div class="form-group">
                        <label>承运商</label>
                        <select id="carrierSelect" class="form-control">
                            <option value="">所有承运商</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>状态</label>
                        <select id="statusSelect" class="form-control">
                            <option value="">所有状态</option>
                            <option value="ACTIVE">有效</option>
                            <option value="INACTIVE">无效</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn primary">
                            <i class='bx bx-search icon'></i>
                            搜索
                        </button>
                        <button type="button" id="resetBtn" class="btn secondary">
                            <i class='bx bx-reset icon'></i>
                            重置
                        </button>
                    </div>
                </form>
            </div>

            <div class="table-card">
                <table class="table">
                    <thead>
                        <tr>
                            <th>报价单号</th>
                            <th>供应商</th>
                            <th>承运商</th>
                            <th>报价日期</th>
                            <th>状态</th>
                            <th>有效期自</th>
                            <th>有效期至</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="quotationTableBody">
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">报价详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="quotationDetail"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/list.js"></script>
    <script>
        // 承运商下拉初始化
        fetch('/api/quotations/carriers').then(res=>res.json()).then(list=>{
            const sel = document.getElementById('carrierSelect');
            list.forEach(c=>{
                const opt = document.createElement('option');
                opt.value = c.name;
                opt.text = c.name + (c.displayName ? ' ' + c.displayName : '');
                sel.appendChild(opt);
            });
        });

        // 查询功能
        const form = document.getElementById('searchForm');
        form.onsubmit = function(e){
            e.preventDefault();
            loadList();
        };

        document.getElementById('resetBtn').onclick = function(){
            document.getElementById('carrierSelect').value = '';
            document.getElementById('statusSelect').value = '';
            loadList();
        };

        function renderQuotationTable(quotations) {
            const tbody = document.getElementById('quotationTableBody');
            tbody.innerHTML = '';
            quotations.forEach(item => {
                const statusClass = item.status === 'ACTIVE' ? 'active' : 'inactive';
                const statusText = item.status === 'ACTIVE' ? '有效' : '无效';
                
                tbody.innerHTML += `
                    <tr>
                        <td>${item.quoteNumber || '-'}</td>
                        <td>${item.supplier || '-'}</td>
                        <td>${item.carrierCode || '-'}</td>
                        <td>${item.quoteDate || '-'}</td>
                        <td><span class="status-tag ${statusClass}">${statusText}</span></td>
                        <td>${item.validFrom || '-'}</td>
                        <td>${item.validTo || '-'}</td>
                        <td>
                            <a href="detail.html?id=${item.id}" class="detail-link">
                                <i class='bx bx-detail icon'></i>
                                详情
                            </a>
                        </td>
                    </tr>
                `;
            });
        }

        // 页面加载时默认加载全部
        loadList();

        // tab切换
        document.getElementById('tabRate').onclick = function(){
            this.classList.add('active');
            document.getElementById('tabTransfer').classList.remove('active');
            document.getElementById('rateTableWrap').style.display = '';
            document.getElementById('transferTableWrap').style.display = 'none';
        };
        document.getElementById('tabTransfer').onclick = function(){
            this.classList.add('active');
            document.getElementById('tabRate').classList.remove('active');
            document.getElementById('rateTableWrap').style.display = 'none';
            document.getElementById('transferTableWrap').style.display = '';
        };
    </script>
</body>
</html>
