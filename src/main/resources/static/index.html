<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>物流报价规则解析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            min-height: 100vh;
            width: 100vw;
            background: url('image/pyUPPlO7.jpg') no-repeat center center fixed;
            background-size: cover;
            position: relative;
        }
        .nav-bar {
            width: 100%;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 10;
            background: rgba(255,255,255,0.92);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            padding: 16px 0 16px 0;
            display: flex;
            justify-content: center;
        }
        .nav-buttons {
            display: flex;
            gap: 12px;
        }
        .nav-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s;
            background: #fff;
        }
        .nav-btn:hover {
            background: #f5f7fa;
            color: #1890ff;
        }
        .nav-btn.primary {
            background: #1890ff;
            color: #fff;
            border-color: #1890ff;
        }
        .nav-btn.primary:hover {
            background: #40a9ff;
            color: #fff;
        }
        .icon {
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="nav-bar">
        <div class="nav-buttons">
            <a href="index.html" class="nav-btn">
                <i class='bx bx-home icon'></i>
                首页
            </a>
            <a href="import.html" class="nav-btn">
                <i class='bx bx-import icon'></i>
                导入运价
            </a>
            <a href="list.html" class="nav-btn">
                <i class='bx bx-list-ul icon'></i>
                运价列表
            </a>
            <a href="freight-query.html" class="nav-btn primary">
                <i class='bx bx-calculator icon'></i>
                运价计算
            </a>
            <a href="carriers.html" class="nav-btn">
                <i class='bx bx-building icon'></i>
                承运商管理
            </a>
        </div>
    </div>
</body>
</html>
