<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>物流报价规则解析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            min-height: 100vh;
            width: 100vw;
            background: url('image/pyUPPlO7.jpg') no-repeat center center fixed;
            background-size: cover;
            position: relative;
        }
        .nav-bar {
            width: 100%;
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1000;
            background: rgba(255,255,255,0.95);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 12px 0;
            backdrop-filter: blur(10px);
        }
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .nav-buttons {
            display: flex;
            gap: 12px;
        }
        .auth-nav-buttons {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        .nav-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s;
            background: #fff;
        }
        .nav-btn:hover {
            background: #f5f7fa;
            color: #1890ff;
        }
        .nav-btn.primary {
            background: #1890ff;
            color: #fff;
            border-color: #1890ff;
        }
        .nav-btn.primary:hover {
            background: #40a9ff;
            color: #fff;
        }
        .nav-btn.disabled {
            background: #f5f5f5;
            color: #ccc;
            cursor: not-allowed;
            border-color: #e8e8e8;
        }
        .nav-btn.disabled:hover {
            background: #f5f5f5;
            color: #ccc;
        }
        .icon {
            font-size: 18px;
        }
        .auth-button {
            background: transparent;
            color: #1890ff;
            border: 1px solid #1890ff;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .auth-button:hover {
            background: #1890ff;
            color: white;
            text-decoration: none;
        }
        .auth-button.primary {
            background: #1890ff;
            color: white;
        }
        .auth-button.primary:hover {
            background: #40a9ff;
        }
        .user-info {
            color: #333;
            font-size: 14px;
            margin-right: 10px;
        }
        .main-content {
            margin-top: 80px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: calc(100vh - 80px);
        }
        .hero-section {
            text-align: center;
            background: rgba(255,255,255,0.95);
            padding: 60px 40px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            max-width: 800px;
            margin-bottom: 40px;
        }
        .hero-title {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1890ff;
            margin-bottom: 20px;
        }
        .hero-subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature-card {
            background: rgba(255,255,255,0.9);
            padding: 30px 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 16px rgba(0,0,0,0.05);
        }
        .feature-icon {
            font-size: 3rem;
            color: #1890ff;
            margin-bottom: 15px;
        }
        .feature-title {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .feature-desc {
            color: #666;
            line-height: 1.5;
        }
        .auth-buttons {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }
        .auth-btn {
            padding: 12px 30px;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .auth-btn.login {
            background: #1890ff;
            color: white;
        }
        .auth-btn.login:hover {
            background: #40a9ff;
        }
        .auth-btn.register {
            background: transparent;
            color: #1890ff;
            border: 2px solid #1890ff;
        }
        .auth-btn.register:hover {
            background: #1890ff;
            color: white;
        }
        .user-info {
            display: none;
            background: rgba(255,255,255,0.95);
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        .user-welcome {
            color: #333;
            margin-bottom: 15px;
        }
        .logout-btn {
            background: #ff4d4f;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        .logout-btn:hover {
            background: #ff7875;
        }
        .nav-buttons.logged-in .nav-btn:not(.primary) {
            pointer-events: auto;
            opacity: 1;
        }
        .nav-buttons:not(.logged-in) .nav-btn:not(.primary) {
            pointer-events: none;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <div class="nav-bar">
        <div class="nav-container">
            <div class="nav-buttons" id="navButtons">
                <a href="index.html" class="nav-btn primary">
                    <i class='bx bx-home icon'></i>
                    首页
                </a>
                <a href="import.html" class="nav-btn" id="importBtn">
                    <i class='bx bx-import icon'></i>
                    导入运价
                </a>
                <a href="list.html" class="nav-btn" id="listBtn">
                    <i class='bx bx-list-ul icon'></i>
                    运价列表
                </a>
                <a href="freight-query.html" class="nav-btn" id="queryBtn">
                    <i class='bx bx-calculator icon'></i>
                    运价计算
                </a>
                <a href="carriers.html" class="nav-btn" id="carriersBtn">
                    <i class='bx bx-building icon'></i>
                    承运商管理
                </a>
                <a href="users.html" class="nav-btn" id="usersBtn">
                    <i class='bx bx-user icon'></i>
                    用户管理
                </a>
            </div>
            <div class="auth-nav-buttons">
                <!-- 未登录状态 -->
                <div id="authButtons" style="display: none;">
                    <button class="auth-button" onclick="showLoginModal()">登录</button>
                    <button class="auth-button primary" onclick="showRegisterModal()">注册</button>
                </div>
                <!-- 已登录状态 -->
                <div id="userInfo" style="display: none;">
                    <span class="user-info">欢迎您，<span id="currentUser"></span>！</span>
                    <button class="auth-button" onclick="logout()">退出登录</button>
                </div>
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="hero-section">
            <h1 class="hero-title">物流报价规则解析系统</h1>
            <p class="hero-subtitle">
                智能化物流报价管理平台，支持PDF文件解析、运价计算、承运商管理等功能，
                让您的物流报价管理更加高效便捷。
            </p>

            <div class="features">
                <div class="feature-card">
                    <i class='bx bx-file-blank feature-icon'></i>
                    <div class="feature-title">智能PDF解析</div>
                    <div class="feature-desc">自动解析PDF报价文件，提取关键信息，生成结构化数据</div>
                </div>
                <div class="feature-card">
                    <i class='bx bx-calculator feature-icon'></i>
                    <div class="feature-title">运价计算</div>
                    <div class="feature-desc">支持多种运价计算规则，包括直达和中转路线计算</div>
                </div>
                <div class="feature-card">
                    <i class='bx bx-building feature-icon'></i>
                    <div class="feature-title">承运商管理</div>
                    <div class="feature-desc">完善的承运商信息管理，支持增删改查操作</div>
                </div>
                <div class="feature-card">
                    <i class='bx bx-data feature-icon'></i>
                    <div class="feature-title">数据管理</div>
                    <div class="feature-desc">统一的数据管理平台，支持导入导出和历史记录查询</div>
                </div>
            </div>


        </div>
    </div>

    <!-- 登录模态框 -->
    <div class="modal fade" id="loginModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户登录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="loginForm">
                        <div class="mb-3">
                            <label class="form-label">用户名</label>
                            <input type="text" class="form-control" id="loginUsername" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">密码</label>
                            <input type="password" class="form-control" id="loginPassword" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">验证码</label>
                            <div class="d-flex gap-2">
                                <input type="text" class="form-control" id="loginCaptcha" placeholder="请输入验证码" required>
                                <img id="loginCaptchaImg" src="" alt="验证码" style="cursor: pointer; height: 38px; border: 1px solid #ddd; border-radius: 4px;" onclick="refreshLoginCaptcha()">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="login()">登录</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册模态框 -->
    <div class="modal fade" id="registerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户注册</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="registerForm">
                        <div class="mb-3">
                            <label class="form-label">用户名</label>
                            <input type="text" class="form-control" id="registerUsername" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">密码</label>
                            <input type="password" class="form-control" id="registerPassword" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">姓名</label>
                            <input type="text" class="form-control" id="registerName">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">电话</label>
                            <input type="text" class="form-control" id="registerPhone">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">验证码</label>
                            <div class="d-flex gap-2">
                                <input type="text" class="form-control" id="registerCaptcha" placeholder="请输入验证码" required>
                                <img id="registerCaptchaImg" src="" alt="验证码" style="cursor: pointer; height: 38px; border: 1px solid #ddd; border-radius: 4px;" onclick="refreshRegisterCaptcha()">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="register()">注册</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let loginCaptchaId = '';
        let registerCaptchaId = '';
        let currentUser = null;

        // 页面加载时检查登录状态
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
        });

        // 检查登录状态
        async function checkLoginStatus() {
            try {
                const response = await fetch('/api/auth/current');
                const result = await response.json();

                if (result.success && result.user) {
                    currentUser = result.user;
                    updateUIForLoggedInUser();
                } else {
                    updateUIForLoggedOutUser();
                }
            } catch (error) {
                console.error('检查登录状态失败:', error);
                updateUIForLoggedOutUser();
            }
        }

        // 更新UI为已登录状态
        function updateUIForLoggedInUser() {
            // 隐藏登录/注册按钮，显示用户信息
            document.getElementById('authButtons').style.display = 'none';
            document.getElementById('userInfo').style.display = 'flex';
            document.getElementById('currentUser').textContent = currentUser.name || currentUser.username;

            // 启用导航按钮
            document.getElementById('navButtons').classList.add('logged-in');
            const navBtns = document.querySelectorAll('.nav-btn:not(.primary)');
            navBtns.forEach(btn => {
                btn.classList.remove('disabled');
            });

            // 根据用户权限控制特定按钮的显示
            updateNavigationByRole();
        }

        // 更新UI为未登录状态
        function updateUIForLoggedOutUser() {
            // 显示登录/注册按钮，隐藏用户信息
            document.getElementById('authButtons').style.display = 'flex';
            document.getElementById('userInfo').style.display = 'none';

            // 禁用导航按钮
            document.getElementById('navButtons').classList.remove('logged-in');
            const navBtns = document.querySelectorAll('.nav-btn:not(.primary)');
            navBtns.forEach(btn => {
                btn.classList.add('disabled');
            });

            // 隐藏所有需要权限的按钮
            hideAdminOnlyButtons();

            currentUser = null;
        }

        // 根据用户角色更新导航栏
        function updateNavigationByRole() {
            if (!currentUser) {
                hideAdminOnlyButtons();
                return;
            }

            // 只有realname为Admin的用户才能看到承运商管理和用户管理按钮
            if (currentUser.realname === 'Admin') {
                showAdminOnlyButtons();
            } else {
                hideAdminOnlyButtons();
            }
        }

        // 显示管理员专用按钮
        function showAdminOnlyButtons() {
            const carrierBtn = document.getElementById('carriersBtn');
            const usersBtn = document.getElementById('usersBtn');
            if (carrierBtn) {
                carrierBtn.style.display = 'inline-flex';
            }
            if (usersBtn) {
                usersBtn.style.display = 'inline-flex';
            }
        }

        // 隐藏管理员专用按钮
        function hideAdminOnlyButtons() {
            const carrierBtn = document.getElementById('carriersBtn');
            const usersBtn = document.getElementById('usersBtn');
            if (carrierBtn) {
                carrierBtn.style.display = 'none';
            }
            if (usersBtn) {
                usersBtn.style.display = 'none';
            }
        }

        // 显示登录模态框
        function showLoginModal() {
            refreshLoginCaptcha();
            new bootstrap.Modal(document.getElementById('loginModal')).show();
        }

        // 显示注册模态框
        function showRegisterModal() {
            refreshRegisterCaptcha();
            new bootstrap.Modal(document.getElementById('registerModal')).show();
        }

        // 刷新登录验证码
        async function refreshLoginCaptcha() {
            try {
                const response = await fetch('/api/auth/captcha');
                const result = await response.json();
                loginCaptchaId = result.captchaId;
                document.getElementById('loginCaptchaImg').src = result.image;
            } catch (error) {
                console.error('获取验证码失败:', error);
                alert('获取验证码失败');
            }
        }

        // 刷新注册验证码
        async function refreshRegisterCaptcha() {
            try {
                const response = await fetch('/api/auth/captcha');
                const result = await response.json();
                registerCaptchaId = result.captchaId;
                document.getElementById('registerCaptchaImg').src = result.image;
            } catch (error) {
                console.error('获取验证码失败:', error);
                alert('获取验证码失败');
            }
        }

        // 用户登录
        async function login() {
            const username = document.getElementById('loginUsername').value.trim();
            const password = document.getElementById('loginPassword').value.trim();
            const captchaCode = document.getElementById('loginCaptcha').value.trim();

            if (!username || !password || !captchaCode) {
                alert('请填写完整信息');
                return;
            }

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        captchaId: loginCaptchaId,
                        captchaCode: captchaCode
                    })
                });

                const result = await response.json();

                if (result.success) {
                    currentUser = result.user;
                    updateUIForLoggedInUser();
                    bootstrap.Modal.getInstance(document.getElementById('loginModal')).hide();

                    // 清空表单
                    document.getElementById('loginForm').reset();

                    alert('登录成功！');
                } else {
                    alert(result.message || '登录失败');
                    refreshLoginCaptcha(); // 刷新验证码
                }
            } catch (error) {
                console.error('登录失败:', error);
                alert('登录失败，请稍后重试');
                refreshLoginCaptcha(); // 刷新验证码
            }
        }

        // 用户注册
        async function register() {
            const username = document.getElementById('registerUsername').value.trim();
            const password = document.getElementById('registerPassword').value.trim();
            const name = document.getElementById('registerName').value.trim();
            const phone = document.getElementById('registerPhone').value.trim();
            const captchaCode = document.getElementById('registerCaptcha').value.trim();

            if (!username || !password || !captchaCode) {
                alert('请填写用户名、密码和验证码');
                return;
            }

            try {
                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        name: name,
                        phone: phone,
                        captchaId: registerCaptchaId,
                        captchaCode: captchaCode
                    })
                });

                const result = await response.json();

                if (result.success) {
                    bootstrap.Modal.getInstance(document.getElementById('registerModal')).hide();

                    // 清空表单
                    document.getElementById('registerForm').reset();

                    alert('注册成功！请登录');
                    showLoginModal();
                } else {
                    alert(result.message || '注册失败');
                    refreshRegisterCaptcha(); // 刷新验证码
                }
            } catch (error) {
                console.error('注册失败:', error);
                alert('注册失败，请稍后重试');
                refreshRegisterCaptcha(); // 刷新验证码
            }
        }

        // 用户登出
        async function logout() {
            try {
                const response = await fetch('/api/auth/logout', {
                    method: 'POST'
                });

                const result = await response.json();

                if (result.success) {
                    updateUIForLoggedOutUser();
                    alert('已退出登录');
                } else {
                    alert('退出登录失败');
                }
            } catch (error) {
                console.error('退出登录失败:', error);
                // 即使请求失败，也更新UI状态
                updateUIForLoggedOutUser();
                alert('已退出登录');
            }
        }

        // 阻止未登录用户访问其他页面，以及权限检查
        document.addEventListener('click', function(e) {
            const clickedBtn = e.target.closest('.nav-btn:not(.primary)');
            if (!clickedBtn) return;

            // 检查是否登录
            if (!currentUser) {
                e.preventDefault();
                alert('请先登录后再使用此功能');
                showLoginModal();
                return;
            }

            // 检查管理员权限
            if ((clickedBtn.id === 'carriersBtn' || clickedBtn.id === 'usersBtn') && currentUser.realname !== 'Admin') {
                e.preventDefault();
                alert('您没有权限访问此功能，仅管理员可用');
                return;
            }
        });
    </script>
</body>
</html>
