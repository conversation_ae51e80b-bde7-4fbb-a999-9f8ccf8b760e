<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>运价详情</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #1890ff;
            --border-color: #e8e8e8;
            --bg-color: #f5f7fa;
            --text-color: #333;
            --text-secondary: #666;
        }
        
        body { 
            margin: 0; 
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            background: var(--bg-color);
            color: var(--text-color);
        }
        
        .main-flex { 
            display: flex; 
            height: 100vh;
            background: #fff;
        }
        
        .left-pdf { 
            flex: 1; 
            border-right: 1px solid var(--border-color);
            background: #fafafa;
            position: relative;
        }
        
        .right-info { 
            flex: 1; 
            padding: 24px;
            overflow-y: auto;
            background: #fff;
        }
        
        .info-card { 
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            padding: 24px;
            margin-bottom: 24px;
            border: 1px solid var(--border-color);
        }
        
        .info-title { 
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--text-color);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .info-row { 
            margin-bottom: 12px;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .tag { 
            display: inline-flex;
            align-items: center;
            background: #e6f7ff;
            color: var(--primary-color);
            border-radius: 4px;
            padding: 4px 12px;
            font-size: 13px;
            margin-right: 8px;
            border: 1px solid #91d5ff;
        }
        
        .table-wrap { 
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 24px;
            border: 1px solid var(--border-color);
        }
        
        table { 
            border-collapse: collapse;
            width: 100%;
            font-size: 14px;
        }
        
        th, td { 
            border: 1px solid var(--border-color);
            padding: 12px;
            text-align: center;
        }
        
        th { 
            background: var(--bg-color);
            font-weight: 500;
            color: var(--text-color);
        }
        
        .nav-tabs { 
            margin-bottom: 16px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            gap: 16px;
        }
        
        .nav-link { 
            cursor: pointer;
            padding: 8px 16px;
            border: none;
            background: none;
            color: var(--text-secondary);
            font-weight: 500;
            position: relative;
            transition: all 0.3s;
        }
        
        .nav-link:hover {
            color: var(--primary-color);
        }
        
        .nav-link.active { 
            color: var(--primary-color);
        }
        
        .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--primary-color);
        }
        
        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            color: var(--text-color);
            text-decoration: none;
            transition: all 0.3s;
            margin-bottom: 16px;
        }
        
        .back-btn:hover {
            background: var(--bg-color);
            color: var(--primary-color);
        }
        
        .icon {
            font-size: 16px;
        }
    </style>
</head>
<body>
<div class="main-flex">
    <!-- 左侧PDF预览 -->
    <div class="left-pdf">
        <iframe id="pdfFrame" width="100%" height="100%" frameborder="0"></iframe>
    </div>
    <!-- 右侧信息 -->
    <div class="right-info">
        <a href="list.html" class="back-btn">
            <i class='bx bx-arrow-back icon'></i>
            返回列表
        </a>
        
        <div class="info-card">
            <div class="info-title">
                <i class='bx bx-file icon'></i>
                <span id="title"></span>
            </div>
            <div id="dateRange" class="info-row">
                <i class='bx bx-calendar icon'></i>
                <span></span>
            </div>
            <div id="remarks" class="info-row">
                <i class='bx bx-message-square-detail icon'></i>
                <span></span>
            </div>
            <div style="margin-top: 12px;">
                <span class="tag" id="currencyTag"></span>
                <span class="tag" id="statusTag"></span>
            </div>
        </div>
        
        <div class="info-card">
            <div class="nav-tabs" id="tabNav">
                <button class="nav-link active" id="rateTab">
                    <i class='bx bx-table icon'></i>
                    运价明细
                </button>
                <button class="nav-link" id="transferTab">
                    <i class='bx bx-transfer icon'></i>
                    中转费
                </button>
            </div>
            
            <div class="table-wrap" id="rateTableWrap">
                <div id="rateTable"></div>
            </div>
            <div class="table-wrap" id="transferTableWrap" style="display:none;">
                <div id="transferTable"></div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // 获取URL参数中的id
    function getQueryVariable(variable) {
        var query = window.location.search.substring(1);
        var vars = query.split("&");
        for (var i=0;i<vars.length;i++) {
            var pair = vars[i].split("=");
            if(pair[0] === variable){return pair[1];}
        }
        return null;
    }
    const quotationId = getQueryVariable('id');
    // 加载详情
    fetch(`/api/quotations/${quotationId}`)
        .then(res => res.json())
        .then(data => {
            // 标题
            document.getElementById('title').innerText = `运价详情 - ${data.quoteNumber || ''} (${data.vendorCode || ''})`;
            // 有效期
            document.getElementById('dateRange').innerText = `有效期：${data.validFrom || ''} 至 ${data.validTo || ''}`;
            // 备注
            document.getElementById('remarks').innerHTML = (data.remarks||[]).map(r=>r.content).join('<br>');
            // 标签
            document.getElementById('currencyTag').innerText = data.currency || '';
            document.getElementById('statusTag').innerText = data.status === 'ACTIVE' ? '有效' : (data.status||'');
            // PDF
            if(data.sourceFile){
                document.getElementById('pdfFrame').src = data.sourceFile;
            }
            // 运价明细表格
            let rateHtml = `<table class="table table-bordered"><tr><th>始发地</th><th>目的地</th><th>密度</th><th>100.0-300.0</th><th>300.0-500.0</th><th>500.0-1000.0</th></tr>`;
            (data.routes||[]).forEach(route => {
                // 按密度分组
                let densityMap = {};
                (route.rates||[]).forEach(rate => {
                    if(!densityMap[rate.densityFactor]) densityMap[rate.densityFactor] = [];
                    densityMap[rate.densityFactor].push(rate);
                });
                Object.keys(densityMap).forEach(density => {
                    let rates = densityMap[density];
                    let row = [`<td>${route.origin}</td>`,`<td>${route.destination}</td>`,`<td>${density}</td>`];
                    // 匹配区间
                    let price100_300 = rates.find(r=>r.minWt==100 && r.maxWt==300)?.price||'';
                    let price300_500 = rates.find(r=>r.minWt==300 && r.maxWt==500)?.price||'';
                    let price500_1000 = rates.find(r=>r.minWt==500 && r.maxWt==1000)?.price||'';
                    row.push(`<td>${price100_300}</td>`,`<td>${price300_500}</td>`,`<td>${price500_1000}</td>`);
                    rateHtml += `<tr>${row.join('')}</tr>`;
                });
            });
            rateHtml += `</table>`;
            document.getElementById('rateTable').innerHTML = rateHtml;
            // 中转费表格
            let transferHtml = `<table class="table table-bordered"><tr><th>始发地</th><th>目的地</th><th>中转口岸</th><th>费用</th><th>币种</th></tr>`;
            (data.routes||[]).forEach(route => {
                (route.transfers||[]).forEach(transfer => {
                    (transfer.destinations||[]).forEach(dest => {
                        transferHtml += `<tr>
                            <td>${route.origin}</td>
                            <td>${dest.toPort}</td>
                            <td>${transfer.fromPort}</td>
                            <td>${transfer.transferCost}</td>
                            <td>${transfer.costCurrency}</td>
                        </tr>`;
                    });
                });
            });
            transferHtml += `</table>`;
            document.getElementById('transferTable').innerHTML = transferHtml;
        });
    // Tab切换
    document.getElementById('rateTab').onclick = function(){
        document.getElementById('rateTableWrap').style.display = '';
        document.getElementById('transferTableWrap').style.display = 'none';
        this.classList.add('active');
        document.getElementById('transferTab').classList.remove('active');
    };
    document.getElementById('transferTab').onclick = function(){
        document.getElementById('rateTableWrap').style.display = 'none';
        document.getElementById('transferTableWrap').style.display = '';
        this.classList.add('active');
        document.getElementById('rateTab').classList.remove('active');
    };
</script>
</body>
</html> 