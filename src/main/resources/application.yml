server:
  port: 8080

spring:
  datasource:
    url: ************************************************************************************************************************
    username: root
    password: zhang<PERSON>@1996
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    database-platform: org.hibernate.dialect.MySQL8Dialect
    hibernate:
      ddl-auto: none  # 禁用自动创建表，使用schema.sql
    show-sql: true
    properties:
      hibernate:
        format_sql: true

  sql:
    init:
      mode: always  # 启用SQL初始化，执行schema.sql
      platform: mysql
      continue-on-error: true
      separator: ;

  servlet:
    multipart:
      enabled: true
      max-file-size: 100MB
      max-request-size: 100MB

  h2:
    console:
      enabled: true
      path: /h2-console

file:
  upload-dir: src/main/resources/static/uploads
  base-url: /uploads

logging:
  level:
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.example: DEBUG
bailian:
  api:
    key: sk-4a36f7bac77043b28ecc84ac6a2d2cd5
    url: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions