-- 创建数据库表结构

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '账号',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    phone VARCHAR(20) COMMENT '电话',
    name VARCHAR(50) COMMENT '姓名',
    realname VARCHAR(50) DEFAULT 'query' COMMENT '真实身份/角色',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 承运商表
CREATE TABLE IF NOT EXISTS carriers (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL COMMENT '承运商名称',
    code VARCHAR(255) UNIQUE NOT NULL COMMENT '承运商代码',
    address VARCHAR(500) COMMENT '地址',
    phone VARCHAR(50) COMMENT '电话',
    contacts VARCHAR(100) COMMENT '联系人',
    createtime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatetime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='承运商表';

-- 报价单主表
CREATE TABLE IF NOT EXISTS quotation_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    currency VARCHAR(10) NOT NULL COMMENT '货币',
    quote_date DATE NOT NULL COMMENT '报价日期',
    quote_number VARCHAR(100) UNIQUE NOT NULL COMMENT '报价单号',
    source_file VARCHAR(500) COMMENT '源文件',
    status VARCHAR(20) NOT NULL COMMENT '状态',
    valid_from DATE NOT NULL COMMENT '有效期开始',
    valid_to DATE NOT NULL COMMENT '有效期结束',
    vendor_code VARCHAR(50) NOT NULL COMMENT '供应商代码',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报价单主表';

-- 备注表
CREATE TABLE IF NOT EXISTS remarks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    content TEXT NOT NULL COMMENT '备注内容',
    is_important BOOLEAN DEFAULT FALSE COMMENT '是否重要',
    type VARCHAR(50) COMMENT '备注类型',
    quotation_id BIGINT NOT NULL COMMENT '报价单ID',
    FOREIGN KEY (quotation_id) REFERENCES quotation_records(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='备注表';

-- 路线表
CREATE TABLE IF NOT EXISTS routes (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    m_rate DOUBLE DEFAULT 0 COMMENT 'M费率',
    n_rate DOUBLE DEFAULT 0 COMMENT 'N费率',
    carrier_code VARCHAR(50) COMMENT '承运商代码',
    destination VARCHAR(50) NOT NULL COMMENT '目的港',
    frequency VARCHAR(50) COMMENT '频率',
    origin VARCHAR(50) NOT NULL COMMENT '起运港',
    quotation_id BIGINT NOT NULL COMMENT '报价单ID',
    FOREIGN KEY (quotation_id) REFERENCES quotation_records(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='路线表';

-- 费率表
CREATE TABLE IF NOT EXISTS rates (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    density_factor DOUBLE NOT NULL COMMENT '密度系数',
    density_type VARCHAR(10) COMMENT '密度类型',
    max_wt DOUBLE NOT NULL COMMENT '最大重量',
    min_wt DOUBLE NOT NULL COMMENT '最小重量',
    price DOUBLE NOT NULL COMMENT '价格',
    route_id BIGINT NOT NULL COMMENT '路线ID',
    FOREIGN KEY (route_id) REFERENCES routes(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='费率表';

-- 中转表
CREATE TABLE IF NOT EXISTS transfers (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    cost_currency VARCHAR(10) COMMENT '费用货币',
    from_port VARCHAR(50) NOT NULL COMMENT '中转起始港',
    transfer_cost DOUBLE NOT NULL COMMENT '中转费用',
    route_id BIGINT NOT NULL COMMENT '路线ID',
    FOREIGN KEY (route_id) REFERENCES routes(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='中转表';

-- 中转目的地表
CREATE TABLE IF NOT EXISTS transfer_destinations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    to_port VARCHAR(50) NOT NULL COMMENT '中转目的港',
    transfer_id BIGINT NOT NULL COMMENT '中转ID',
    FOREIGN KEY (transfer_id) REFERENCES transfers(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='中转目的地表';

-- 运费计算表
CREATE TABLE IF NOT EXISTS freight_calculations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    quotation_id BIGINT COMMENT '报价单ID',
    quote_number VARCHAR(100) COMMENT '报价单号',
    volume DOUBLE COMMENT '体积',
    weight DOUBLE COMMENT '重量',
    density DOUBLE COMMENT '密度',
    rate_id BIGINT COMMENT '费率ID',
    origin VARCHAR(50) COMMENT '起运港',
    destination VARCHAR(50) COMMENT '目的港',
    carrier_code VARCHAR(50) COMMENT '承运商代码',
    airline VARCHAR(50) COMMENT '航司',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='运费计算表';

-- 创建索引
CREATE INDEX idx_quotation_records_quote_number ON quotation_records(quote_number);
CREATE INDEX idx_quotation_records_vendor_code ON quotation_records(vendor_code);
CREATE INDEX idx_routes_quotation_id ON routes(quotation_id);
CREATE INDEX idx_routes_origin_destination ON routes(origin, destination);
CREATE INDEX idx_rates_route_id ON rates(route_id);
CREATE INDEX idx_transfers_route_id ON transfers(route_id);
CREATE INDEX idx_transfer_destinations_transfer_id ON transfer_destinations(transfer_id);
CREATE INDEX idx_carriers_name ON carriers(name);
CREATE INDEX idx_carriers_code ON carriers(code);